"use client";

/* eslint-disable */

import { Fragment, useContext, useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  DeleteRequests,
  GetRequest,
  PatchRequest,
  PostRequest,
} from "~/utils/request";
import Loading from "~/components/ui/loading";
import { Checkbox } from "~/components/ui/checkbox";
import { useParams } from "next/navigation";
import cogoToast from "cogo-toast";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import axios from "axios";

export default function Output({
  integration,
  specifiedChannels,
  outputIntegration,
}: any) {
  const params = useParams();
  const id = params.id as string;
  const id2 = params.id2 as string;
  const { state, dispatch } = useContext(DataContext);
  const [outputChannels, setOutputChannels] = useState<any>([]);
  const [selectedOutput, setSelectedOutput] = useState<any>(null);
  const [connectloading, setConnectloading] = useState(false);
  const [integrationChannel, setIntegrationChannel] = useState<any>(null);
  const [sendback, setSendback] = useState("");

  // Fetch integrations
  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";
    const token = localStorage.getItem("token") || "";
    const sendback = localStorage.getItem("sendback") || "";

    setSendback(sendback);

    const getIntegrations = async () => {
      try {
        const res = await GetRequest(
          `/organisations/${orgId}/integrations`,
          token
        );

        if (res?.status === 200 || res?.status === 201) {
          // setIntegrations(res?.data?.data);
        }
      } catch (error) {
        console.error("Error fetching integrations:", error);
      }
    };

    getIntegrations();
  }, []);

  // Fetch output integration channel
  const getIntegrationChannels = async (value: any) => {
    const orgId = localStorage.getItem("orgId") || "";

    try {
      const res = await axios.get(
        `${value?.channels_url}?organisation_id=${orgId}`
      );
      setOutputChannels(res?.data?.data);
    } catch (err) {
      setOutputChannels([]);
    }
  };

  const handleSelectChange = (value: string) => {
    setSelectedOutput(JSON.parse(value));
    getIntegrationChannels(JSON.parse(value));
  };

  // handle connect
  const handleConnect = async () => {
    setConnectloading(true);

    const token = localStorage.getItem("token") || "";

    const payload = {
      int_modifier_id: id2,
      int_output_id: selectedOutput?.id,
      int_channel_name: integrationChannel?.name,
      int_channel_id: integrationChannel?.id,
    };

    const res = await PostRequest(
      `/channels/${id}/integration-channels`,
      payload,
      token
    );
    if (res?.status === 200 || res?.status === 201) {
      dispatch({ type: ACTIONS.APP_CALLBACK, payload: !state?.appCallback });
      cogoToast.success(res?.data?.message);
    }

    setConnectloading(false);
  };

  const handleDeactivateIntegrationChannel = async (
    int_channel_id: string,
    outputId: string
  ) => {
    setConnectloading(true);

    const token = localStorage.getItem("token") || "";

    const payload = {
      int_modifier_id: id2,
      int_output_id: outputId,
      int_channel_id: int_channel_id,
    };

    const res = await DeleteRequests(
      `/channels/${id}/integration-channels`,
      payload,
      token
    );
    if (res?.status === 200 || res?.status === 201) {
      dispatch({ type: ACTIONS.APP_CALLBACK, payload: !state?.appCallback });
      cogoToast.success(res?.data?.message);
    }
  };

  // send back
  const handleSendBack = async (isChecked: boolean) => {
    const token = localStorage.getItem("token") || "";
    const orgId = localStorage.getItem("orgId") || "";
    const sendback = localStorage.getItem("sendback") || "";

    const payload = {
      integration_id: id2,
      status: sendback === "true" ? false : true,
    };

    const res = await PatchRequest(
      `/organisations/${orgId}/channels/${id}/integrations/change-sendback-status`,
      payload,
      token
    );
    if (res?.status === 200 || res?.status === 201) {
      localStorage.setItem("sendback", sendback === "true" ? "false" : "true");
      setSendback(sendback === "true" ? "false" : "true");
      cogoToast.success(res?.data?.message);
      console.log(res?.data);
    }
  };

  //

  return (
    <div className="text-neutral-600 pb-4 mb-20">
      <div className="space-y-4 mb-6">
        <label className="block mb-2 text-sm text-black">
          Send back to channel
        </label>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="sendBack"
            checked={sendback === "true" ? true : false}
            onCheckedChange={(isChecked: boolean) => handleSendBack(isChecked)}
            className="size-5"
          />
          <label
            htmlFor="sendBack"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Send Back
          </label>
        </div>
      </div>

      {integration?.integration_type === "m" && (
        <>
          <div className="flex items-end gap-5">
            <div className="w-60">
              <label className="block mb-2 text-sm text-black">
                Select Integration
              </label>

              <Select
                onValueChange={handleSelectChange}
                value={
                  selectedOutput ? JSON.stringify(selectedOutput) : undefined
                }
              >
                <SelectTrigger className="h-10 text-primary focus:outline-none focus:ring-0 focus:ring-primary focus-visible:ring-0 focus-visible:ring-primary">
                  <SelectValue placeholder="select integration" />
                </SelectTrigger>

                <SelectContent>
                  {outputIntegration?.map((item: any, index: number) => (
                    <SelectItem value={JSON.stringify(item)} key={index}>
                      {item?.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {outputChannels?.length >= 1 && (
              <div className="w-60">
                <label className="block mb-2 text-sm text-black">
                  Select Integration Channel
                </label>

                <Select
                  onValueChange={(value) =>
                    setIntegrationChannel(JSON.parse(value))
                  }
                  value={
                    integrationChannel
                      ? JSON.stringify(integrationChannel)
                      : undefined
                  }
                >
                  <SelectTrigger className="h-10 text-primary focus:outline-none focus:ring-0 focus:ring-primary focus-visible:ring-0 focus-visible:ring-primary">
                    <SelectValue placeholder="Slack channels" />
                  </SelectTrigger>

                  <SelectContent>
                    {outputChannels?.map((item: any, index: number) => (
                      <SelectItem value={JSON.stringify(item)} key={index}>
                        {item?.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="">
              <button
                disabled={!integrationChannel || outputChannels?.length === 0}
                onClick={handleConnect}
                className="px-6 rounded-md bg-primary-500 hover:bg-opacity-80 text-white font-medium text-sm h-10 disabled:cursor-not-allowed disabled:bg-opacity-50 "
              >
                {connectloading ? (
                  <span className="flex items-center gap-x-2">
                    <span className="animate-pulse">Loading</span>{" "}
                    <Loading width="20" height="40" />
                  </span>
                ) : (
                  "Connect"
                )}
              </button>
            </div>
          </div>

          {/* show specified channels for modification integrations */}
          {specifiedChannels?.map((item: any, index: number) => {
            return (
              <Fragment key={index}>
                <div className="space-y-4 my-10">
                  <label className="block mb-2 text-base text-black font-medium">
                    {item?.integration_name}
                  </label>

                  {item?.integration_channels?.map(
                    (data: any, index: number) => (
                      <div
                        key={item.channel_id}
                        className="flex items-center space-x-4"
                      >
                        <Checkbox
                          id={data.id}
                          checked={true}
                          onCheckedChange={() =>
                            handleDeactivateIntegrationChannel(
                              data?.int_channel_id,
                              item?.integration_output_id
                            )
                          }
                          className="size-5"
                        />
                        <label
                          htmlFor={data?.id}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {data?.int_channel_name}
                        </label>
                      </div>
                    )
                  )}
                </div>
                <hr />
              </Fragment>
            );
          })}
        </>
      )}
    </div>
  );
}
