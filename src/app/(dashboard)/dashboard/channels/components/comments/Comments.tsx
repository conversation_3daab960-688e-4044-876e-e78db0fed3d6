"use client";
import Image from "next/image";
import React, { FormEvent, useContext, useEffect, useState } from "react";
import { GetRequest, PostRequest } from "~/utils/request";
import Wysiwyg from "../wysiwyg/Wysiwyg";
import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Mention from "@tiptap/extension-mention";
import Code from "@tiptap/extension-code";
import CodeBlock from "@tiptap/extension-code-block";
import axios from "axios";
import { Centrifuge, Subscription } from "centrifuge";
import { useParams } from "next/navigation";
import { format } from "timeago.js";
import ListItem from "@tiptap/extension-list-item";
import Link from "@tiptap/extension-link";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import InfiniteScroll from "react-infinite-scroll-component";
import HardBreak from "@tiptap/extension-hard-break";
import Paragraph from "@tiptap/extension-paragraph";
import { getInitials } from "~/utils/utils";

/* eslint-disable */

const Comments = () => {
  const [messages, setMessages] = useState<any>([]);
  const params = useParams();
  const id = params.id as string;
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [, setMentionQuery] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const { state, dispatch } = useContext(DataContext);

  const connectUrl: any = process.env.NEXT_PUBLIC_CONNECT_URL;

  const data = [
    { title: "Event Name", text: state?.thread?.event_name },
    { title: "Status", text: state?.thread?.status },
  ];

  // Get connection token
  const getConnectionToken = async () => {
    const token = localStorage.getItem("token") || "";
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/connection`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  // Fetch subscription token
  const getSubscriptionToken = async (thread: string) => {
    const token = localStorage.getItem("token") || "";

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/subscription`,
      { channel: thread },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          HTMLAttributes: {
            class: "list-disc",
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: "list-decimal",
          },
        },
        paragraph: false,
      }),
      Paragraph,
      HardBreak.configure({
        keepMarks: true,
      }),
      Placeholder.configure({
        placeholder: "Reply...",
      }),
      Mention.configure({
        HTMLAttributes: {
          class: "mention",
          style: "color: blue; font-weight: normal;",
        },
        deleteTriggerWithBackspace: true,
        suggestion: {
          items: (query) => {
            const queryString = String(query || "").toLowerCase();
            return state?.orgMembers?.filter((item: any) => {
              let name =
                item?.name && item?.name !== " " ? item?.name : item?.email;
              return name.toLowerCase().includes(queryString);
            });
          },
          render: () => {
            let component: HTMLElement | null = null;

            const handleKeyDown = (event: any) => {
              // Close the modal if Escape is pressed
              if (event.key === "Escape") {
                if (component) {
                  component.remove();
                  component = null;
                }
              }
            };

            const handleClickOutside = (event: any) => {
              if (component && !component.contains(event.target)) {
                component.remove();
                component = null;
              }
            };

            return {
              onStart: ({ query, command, clientRect }) => {
                component = document.createElement("div");
                component.className =
                  "absolute bottom-20 border border-gray-300 rounded-lg shadow-lg max-h-60 bg-white overflow-y-auto z-10 w-[350px]";
                document.body.appendChild(component);

                document.addEventListener("keydown", handleKeyDown);
                document.addEventListener("mousedown", handleClickOutside);

                const filteredItems = state?.orgMembers?.filter((item: any) =>
                  item.name.toLowerCase().includes(String(query).toLowerCase())
                );

                const coords = clientRect ? clientRect() : null;
                if (coords) {
                  const editorTop = coords.top + window.scrollY;

                  const dropdownHeight =
                    filteredItems?.length > 5
                      ? 250
                      : filteredItems?.length >= 3
                        ? 130
                        : 100;

                  const isAbove = editorTop > dropdownHeight;
                  const dropdownTop = isAbove
                    ? editorTop - dropdownHeight
                    : coords.bottom + window.scrollY;

                  Object.assign(component.style, {
                    top: `${dropdownTop}px`,
                    left: `${coords.left + window.scrollX}px`,
                  });
                } else {
                  console.warn(
                    "clientRect returned null; modal positioning skipped."
                  );
                }

                filteredItems?.forEach((item: any) => {
                  const button = document.createElement("button");
                  button.className =
                    "flex items-center px-4 py-2 text-left hover:bg-gray-100 w-full";

                  // Check if avatar_url exists, otherwise show initials
                  const avatarContainer = document.createElement("div");
                  avatarContainer.className =
                    "w-6 h-6 flex items-center justify-center rounded-md bg-primary-500 text-white text-xs font-bold mr-2";
                  avatarContainer.style.minWidth = "1.5rem";

                  if (item.profile_url && item.profile_url !== "") {
                    const img = document.createElement("img");
                    img.src = item.profile_url;
                    img.alt = item.name;
                    img.className = "w-full h-full rounded-md";
                    avatarContainer.appendChild(img);
                  } else {
                    avatarContainer.textContent = getInitials(
                      item?.name && item?.name !== " "
                        ? item?.name
                        : item?.email
                    );
                  }

                  const span = document.createElement("span");
                  span.textContent =
                    item?.name && item?.name !== " " ? item?.name : item?.email;
                  span.className = `${item.name ? "capitalize" : ""} text-xs font-semibold`;

                  button.appendChild(avatarContainer);
                  button.appendChild(span);

                  button.onclick = () => {
                    command({
                      id: item.id,
                      label:
                        item?.name && item?.name !== " "
                          ? item?.name
                          : item?.email,
                    });
                  };

                  component!.appendChild(button);
                });
              },

              onUpdate: ({ query, command, clientRect }) => {
                if (!component) return;

                component.innerHTML = "";

                const filteredItems = state?.orgMembers?.filter((item: any) =>
                  item.name.toLowerCase().includes(String(query).toLowerCase())
                );

                const coords = clientRect ? clientRect() : null;
                if (coords) {
                  const editorTop = coords.top + window.scrollY;
                  const dropdownHeight =
                    filteredItems?.length > 5
                      ? 250
                      : filteredItems?.length >= 3
                        ? 130
                        : 100;

                  const isAbove = editorTop > dropdownHeight;
                  const dropdownTop = isAbove
                    ? editorTop - dropdownHeight
                    : coords.bottom + window.scrollY;

                  Object.assign(component.style, {
                    top: `${dropdownTop}px`,
                    left: `${coords.left + window.scrollX}px`,
                  });
                }

                filteredItems?.forEach((item: any) => {
                  const button = document.createElement("button");
                  button.className =
                    "flex items-center px-4 py-2 text-left hover:bg-gray-100 w-full";

                  // Check if avatar_url exists, otherwise show initials
                  const avatarContainer = document.createElement("div");
                  avatarContainer.className =
                    "w-6 h-6 flex items-center justify-center rounded-md bg-primary-500 text-white text-xs font-bold mr-2";
                  avatarContainer.style.minWidth = "1.5rem";

                  if (item.profile_url && item.profile_url !== "") {
                    const img = document.createElement("img");
                    img.src = item.profile_url;
                    img.alt = item.name;
                    img.className = "w-full h-full rounded-md";
                    avatarContainer.appendChild(img);
                  } else {
                    avatarContainer.textContent = getInitials(
                      item?.name && item?.name !== " "
                        ? item?.name
                        : item?.email
                    );
                  }

                  const span = document.createElement("span");
                  span.textContent =
                    item?.name && item?.name !== " " ? item?.name : item?.email;
                  span.className = `${item.name ? "capitalize" : ""} text-xs font-semibold`;

                  button.appendChild(avatarContainer);
                  button.appendChild(span);

                  button.onclick = () => {
                    command({
                      id: item.id,
                      label:
                        item?.name && item?.name !== " "
                          ? item?.name
                          : item?.email,
                    });
                  };

                  component!.appendChild(button);
                });
              },

              onExit: () => {
                if (component) {
                  component.remove();
                  component = null;
                }

                // Clean up event listeners
                document.removeEventListener("keydown", handleKeyDown);
                document.removeEventListener("mousedown", handleClickOutside);
              },
            };
          },
        },
      }),
      Link.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: "https",
      }),
      Code,
      CodeBlock.configure({
        HTMLAttributes: {
          class: "language-javascript",
        },
      }),
      ListItem,
    ],
  });

  // Centrifugo connection
  useEffect(() => {
    if (state?.thread) {
      const centrifugeClient: any = new Centrifuge(connectUrl, {
        getToken: getConnectionToken,
        debug: true,
      });

      const getPersonalChannelSubscriptionToken = async () => {
        return getSubscriptionToken(state?.thread?.thread_id);
      };

      // Create a subscription to the channel
      const sub = centrifugeClient.newSubscription(state?.thread?.thread_id, {
        getToken: getPersonalChannelSubscriptionToken,
      });

      sub.on("publication", (ctx: any) => {
        // console.log(ctx?.data)
        setMessages((prev: any) => [ctx.data, ...prev]);
      });

      centrifugeClient.connect();
      sub.subscribe();

      setSubscription(sub);

      return () => {
        sub.unsubscribe();
        centrifugeClient.disconnect();
        setSubscription(null);
      };
    }
  }, [state?.thread?.thread_id, connectUrl, state?.thread]);

  // fetch thread messages
  useEffect(() => {
    const token = localStorage.getItem("token") || "";

    // Reset the page number and messages when switching channels
    setPage(1);
    setMessages([]);
    setHasMore(true);

    if (state?.thread?.thread_id) {
      const getMessages = async () => {
        const res = await GetRequest(
          `/threads/${state?.thread?.thread_id}/channels/${id}?page=1&limit=15`,
          token
        );

        if (res?.status == 200 || res?.status == 201) {
          setMessages(res?.data?.data);
        }
      };
      getMessages();
    }
  }, [state?.thread?.thread_id, id]);

  // Handle message submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    const user = JSON.parse(localStorage.getItem("user") || "");
    const token = localStorage.getItem("token") || "";

    let content = editor?.getHTML();
    // content = removeHTMLTags(content);

    const strippedContent = content?.replace(/<[^>]+>/g, "").trim();

    // Check if content is empty or just whitespace
    if (!strippedContent) {
      return;
    }

    if (subscription) {
      editor?.commands?.clearContent();

      // payload
      const payload = {
        content: content,
        channels_id: id,
        user_id: user?.id,
        thread_id: state?.thread?.thread_id,
      };

      // save the data to the database
      await PostRequest(`/channels/${id}/messages`, payload, token);
      dispatch({ type: ACTIONS.CALLBACK, payload: !state?.callback });
    }
  };

  const handleKeyDown = (event: any) => {
    if (event.key === "Enter") {
      if (event.shiftKey) {
        // Insert a new line when Shift + Enter is pressed
        event.preventDefault();
        editor?.commands.enter();
      } else {
        // Submit the form when Enter is pressed
        event.preventDefault();
        handleSubmit(event);
      }
    }
  };

  const fetchThreads = async (newPage: number = 1) => {
    const token = localStorage.getItem("token") || "";

    const res = await GetRequest(
      `/threads/${state?.thread?.thread_id}/channels/${id}?page=${newPage}&limit=10`,
      token
    );

    if (newPage === 1) {
      setMessages(res?.data?.data);
    } else {
      setMessages((prevThreads: any) => [
        ...prevThreads,
        ...(res?.data?.data ?? []),
      ]);
    }
    setPage(newPage);
    setHasMore(
      res?.data?.data.length > 0 && res?.data.pagination.previousPage !== null
    );
  };

  const fetchMoreData = () => {
    if (hasMore) {
      const nextPage = page + 1;
      fetchThreads(nextPage);
    }
  };

  const [isFocused, setIsFocused] = useState(true);

  useEffect(() => {
    if (editor) {
      editor.commands.focus();
    }
  }, [editor]);

  //

  return (
    <div className="">
      <div className="flex items-center justify-between p-4 border-b">
        <h1 className="text-[12px] font-bold text-[#1D2939]">Comments</h1>
        <div className="flex items-center gap-[11px] sticky top-0">
          <div
            className="cursor-pointer"
            onClick={() => dispatch({ type: ACTIONS.VISIBLE, payload: false })}
          >
            <svg
              width="15"
              height="15"
              viewBox="0 0 15 15"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.8536 2.85355C13.0488 2.65829 13.0488 2.34171 12.8536 2.14645C12.6583 1.95118 12.3417 1.95118 12.1464 2.14645L7.5 6.79289L2.85355 2.14645C2.65829 1.95118 2.34171 1.95118 2.14645 2.14645C1.95118 2.34171 1.95118 2.65829 2.14645 2.85355L6.79289 7.5L2.14645 12.1464C1.95118 12.3417 1.95118 12.6583 2.14645 12.8536C2.34171 13.0488 2.65829 13.0488 2.85355 12.8536L7.5 8.20711L12.1464 12.8536C12.3417 13.0488 12.6583 13.0488 12.8536 12.8536C13.0488 12.6583 13.0488 12.3417 12.8536 12.1464L8.20711 7.5L12.8536 2.85355Z"
                fill="currentColor"
                fill-rule="evenodd"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
        </div>
      </div>

      {state?.thread?.type === "thread" && (
        <div className="flex sticky flex-col gap-[26px] pt-3 px-5">
          <tbody className="text-[10px] leading-3 text-neutral-500 text-left">
            {data?.map((item: any) => (
              <tr
                key={item.title}
                className="[&:not(:first-child)]:[--t:6px] [&:not(:last-child)]:[--b:6px] text-xs"
              >
                <th
                  scope="row"
                  className="p-0 pb-[var(--b)] pt-[var(--t)] font-normal align-top text-xs"
                >
                  {item.title}
                </th>
                <th className="p-0 pl-7  pb-[var(--b)] pt-[var(--t)] font-bold align-top text-xs">
                  {item.text}
                </th>
              </tr>
            ))}
          </tbody>
          <hr />
        </div>
      )}

      <div
        id="scrollableDiv"
        style={{
          height: state?.thread?.type === "thread" ? "80vh" : "93vh",
          overflow: "scroll",
          display: "flex",
          flexDirection: "column-reverse",
        }}
        className="pb-40 w-full"
      >
        <InfiniteScroll
          dataLength={messages?.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={
            messages?.length !== 0 &&
            messages?.length >= 15 && (
              <h4 className="my-5 text-xs text-center">Loading threads...</h4>
            )
          }
          style={{
            display: "flex",
            flexDirection: "column-reverse",
            overflow: "visible",
          }}
          scrollableTarget="scrollableDiv"
          inverse={true}
        >
          {messages?.map((item: any, index: number) => (
            <div
              key={index}
              className="flex items-start gap-[7px] w-full p-4 py-3 hover:bg-[#F2F4F7] cursor-pointer"
            >
              {item?.avatar_url ? (
                <div className="size-10 rounded-md border border-neutral-200 flex items-center justify-center overflow-hidden">
                  <Image
                    src={item?.avatar_url}
                    alt="user avatar"
                    width={50}
                    height={50}
                    className="rounded-md size-10"
                    style={{ objectFit: "cover" }}
                  />
                </div>
              ) : (
                <div className="size-10 rounded-md border border-neutral-200 flex items-center justify-center bg-primary-500 text-white font-bold uppercase">
                  {item?.username?.charAt(0) || item?.email?.charAt(0)}
                  {item?.username?.charAt(1) || item?.email?.charAt(1)}
                </div>
              )}

              <div className="w-full flex flex-col items-start">
                <div className="flex gap-4 w-full items-center">
                  <h1 className="text-[13px] text-[#000000] font-[600]">
                    {item?.username || item?.email}
                  </h1>

                  <p className="text-[#98A2B3] text-[11px] font-[400]">
                    {format(item?.created_at)}
                  </p>
                </div>

                <div
                  style={{
                    whiteSpace: "pre-line",
                    wordBreak: "break-word",
                    overflowWrap: "break-word",
                  }}
                  className="text-[#344054] text-[13px] font-[400] leading-relaxed"
                  dangerouslySetInnerHTML={{
                    __html: item?.message
                      .replace(
                        /<ul>/g,
                        '<ul style="padding-left: 10px; list-style-type: disc; list-style-position: outside;">'
                      )
                      .replace(
                        /<ol>/g,
                        '<ol style="padding-left: 10px; list-style-type: decimal; list-style-position: outside;">'
                      )
                      .replace(/<li>/g, '<li style="margin-left: 18px;">'),
                  }}
                />
              </div>
            </div>
          ))}
        </InfiniteScroll>
      </div>

      <div className="absolute w-full bottom-0 left-0 right-0 bg-white">
        <div
          className={`py-[9.5px] px-[12.67px] mt-5 mb-3 border ${
            isFocused ? "border-primary-500" : "border-[#E4E7EC]"
          } rounded-md mx-3 z-10 bg-white`}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        >
          <Wysiwyg
            handleKeyDown={handleKeyDown}
            handleSubmit={handleSubmit}
            editor={editor}
          />
        </div>
      </div>
    </div>
  );
};

export default Comments;
