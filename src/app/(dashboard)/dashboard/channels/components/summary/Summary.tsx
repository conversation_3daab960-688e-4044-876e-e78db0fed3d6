"use client";
import React, { useContext } from "react";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";

const Summary = () => {
  const { state, dispatch } = useContext(DataContext);

  //

  return (
    <div>
      <div className="flex items-center justify-between p-4 border-b">
        <h1 className="text-[14px] font-semibold text-[#1D2939]">Activity</h1>
        <div className="flex items-center gap-[11px] sticky top-0">
          <div
            className="cursor-pointer"
            onClick={() => dispatch({ type: ACTIONS.VISIBLES, payload: false })}
          >
            <svg
              width="15"
              height="15"
              viewBox="0 0 15 15"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.8536 2.85355C13.0488 2.65829 13.0488 2.34171 12.8536 2.14645C12.6583 1.95118 12.3417 1.95118 12.1464 2.14645L7.5 6.79289L2.85355 2.14645C2.65829 1.95118 2.34171 1.95118 2.14645 2.14645C1.95118 2.34171 1.95118 2.65829 2.14645 2.85355L6.79289 7.5L2.14645 12.1464C1.95118 12.3417 1.95118 12.6583 2.14645 12.8536C2.34171 13.0488 2.65829 13.0488 2.85355 12.8536L7.5 8.20711L12.1464 12.8536C12.3417 13.0488 12.6583 13.0488 12.8536 12.8536C13.0488 12.6583 13.0488 12.3417 12.8536 12.1464L8.20711 7.5L12.8536 2.85355Z"
                fill="currentColor"
                fill-rule="evenodd"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
        </div>
      </div>

      <div className="flex sticky flex-col gap-[26px] pt-3 px-5">
        <h2 className="text-md">{state?.thread?.event_name}</h2>
        <hr />

        <SyntaxHighlighter
          language="javascript"
          className="syntax-highlighter-custom"
          useInlineStyles={true}
        >
          {state?.thread?.message}
        </SyntaxHighlighter>
      </div>
    </div>
  );
};

export default Summary;
