"use client";
import Image from "next/image";
import React, {
  FormEvent,
  Fragment,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  DeleteRequest,
  GetRequest,
  PostRequest,
  PutRequest,
} from "~/utils/request";
import Wysiwyg from "../wysiwyg/Wysiwyg";
import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Mention from "@tiptap/extension-mention";
import Code from "@tiptap/extension-code";
import CodeBlock from "@tiptap/extension-code-block";
import axios from "axios";
import { Centrifuge, Subscription } from "centrifuge";
import { useParams } from "next/navigation";
import ListItem from "@tiptap/extension-list-item";
import Link from "@tiptap/extension-link";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import InfiniteScroll from "react-infinite-scroll-component";
import HardBreak from "@tiptap/extension-hard-break";
import Paragraph from "@tiptap/extension-paragraph";
import { getInitials } from "~/utils/utils";
import { uuidv7 } from "uuidv7";
import EditMessageEditor from "../wysiwyg/EditMessage";
import ConfirmModal from "~/telexComponents/ConfirmModal";
import moment from "moment";
import AvatarGroups from "../../../channels/components/avatar";

/* eslint-disable */

const Comments = () => {
  const [messages, setMessages] = useState<any>([]);
  const params = useParams();
  const id = params.id as string;
  const id2 = params.id2 as string;
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [, setMentionQuery] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const { state, dispatch } = useContext(DataContext);
  const uuid = uuidv7();
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const buttonRef = useRef<HTMLDivElement | null>(null);
  const [deleteModal, setDeleteModal] = useState(false);
  const [deleteloading, setDeleteloading] = useState(false);
  const [threadId, setThreadId] = useState("");
  // const [image, setImage] = useState<string | null>(null);
  const [editedMessage, setEditedMessage] = useState<string>("");
  const [isEdit, setIsEdit] = useState(false);
  const [email, setEmail] = useState("");
  const [user, setUser] = useState<any>(null);

  const connectUrl: any = process.env.NEXT_PUBLIC_CONNECT_URL;

  useEffect(() => {
    const email = localStorage.getItem("email") || "";
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    setEmail(email);
    setUser(user);
  }, []);

  // Get connection token
  const getConnectionToken = async () => {
    const token = localStorage.getItem("token") || "";
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/connection`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  // Fetch subscription token
  const getSubscriptionToken = async (thread: string) => {
    const token = localStorage.getItem("token") || "";

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/subscription`,
      { channel: thread },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          HTMLAttributes: {
            class: "list-disc pl-5 space-y-1",
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: "list-decimal pl-5 space-y-1",
          },
        },
        listItem: {
          HTMLAttributes: {
            class: "leading-[0.75] tracking-wide",
          },
        },
        paragraph: {
          HTMLAttributes: {
            class: "leading-[1.75]",
          },
        },
      }),
      Paragraph,
      HardBreak.configure({
        keepMarks: true,
      }),
      Placeholder.configure({
        placeholder: "Reply...",
      }),
      Mention.configure({
        HTMLAttributes: {
          class: "mention",
          style: "color: blue; font-weight: normal;",
        },
        deleteTriggerWithBackspace: true,
        suggestion: {
          items: (query) => {
            const queryString = String(query || "").toLowerCase();
            return state?.orgMembers?.filter((item: any) => {
              let name =
                item?.name && item?.name !== " " ? item?.name : item?.email;
              return name.toLowerCase().includes(queryString);
            });
          },
          render: () => {
            let component: HTMLElement | null = null;

            const handleKeyDown = (event: any) => {
              // Close the modal if Escape is pressed
              if (event.key === "Escape") {
                if (component) {
                  component.remove();
                  component = null;
                }
              }
            };

            const handleClickOutside = (event: any) => {
              if (component && !component.contains(event.target)) {
                component.remove();
                component = null;
              }
            };

            return {
              onStart: ({ query, command, clientRect }) => {
                component = document.createElement("div");
                component.className =
                  "absolute bottom-20 border border-gray-300 rounded-lg shadow-lg max-h-60 bg-white overflow-y-auto z-10 w-[350px]";
                document.body.appendChild(component);

                document.addEventListener("keydown", handleKeyDown);
                document.addEventListener("mousedown", handleClickOutside);

                const filteredItems = state?.orgMembers?.filter((item: any) =>
                  item.name.toLowerCase().includes(String(query).toLowerCase())
                );

                const coords = clientRect ? clientRect() : null;
                if (coords) {
                  const editorTop = coords.top + window.scrollY;

                  const dropdownHeight =
                    filteredItems?.length > 5
                      ? 250
                      : filteredItems?.length >= 3
                        ? 130
                        : 100;

                  const isAbove = editorTop > dropdownHeight;
                  const dropdownTop = isAbove
                    ? editorTop - dropdownHeight
                    : coords.bottom + window.scrollY;

                  Object.assign(component.style, {
                    top: `${dropdownTop}px`,
                    left: `${coords.left + window.scrollX}px`,
                  });
                } else {
                  console.warn(
                    "clientRect returned null; modal positioning skipped."
                  );
                }

                filteredItems?.forEach((item: any) => {
                  const button = document.createElement("button");
                  button.className =
                    "flex items-center px-4 py-2 text-left hover:bg-gray-100 w-full";

                  // Check if avatar_url exists, otherwise show initials
                  const avatarContainer = document.createElement("div");
                  avatarContainer.className =
                    "w-6 h-6 flex items-center justify-center rounded-md bg-primary-500 text-white text-xs font-bold mr-2";
                  avatarContainer.style.minWidth = "1.5rem";

                  if (item.profile_url && item.profile_url !== "") {
                    const img = document.createElement("img");
                    img.src = item.profile_url;
                    img.alt = item.name;
                    img.className = "w-full h-full rounded-md";
                    avatarContainer.appendChild(img);
                  } else {
                    avatarContainer.textContent = getInitials(
                      item?.name && item?.name !== " "
                        ? item?.name
                        : item?.email
                    );
                  }

                  const span = document.createElement("span");
                  span.textContent =
                    item?.name && item?.name !== " " ? item?.name : item?.email;
                  span.className = `${item.name ? "capitalize" : ""} text-xs font-semibold`;

                  button.appendChild(avatarContainer);
                  button.appendChild(span);

                  button.onclick = () => {
                    command({
                      id: item.id,
                      label:
                        item?.name && item?.name !== " "
                          ? item?.name
                          : item?.email,
                    });
                  };

                  component!.appendChild(button);
                });
              },

              onUpdate: ({ query, command, clientRect }) => {
                if (!component) return;

                component.innerHTML = "";

                const filteredItems = state?.orgMembers?.filter((item: any) =>
                  item.name.toLowerCase().includes(String(query).toLowerCase())
                );

                const coords = clientRect ? clientRect() : null;
                if (coords) {
                  const editorTop = coords.top + window.scrollY;
                  const dropdownHeight =
                    filteredItems?.length > 5
                      ? 250
                      : filteredItems?.length >= 3
                        ? 130
                        : 100;

                  const isAbove = editorTop > dropdownHeight;
                  const dropdownTop = isAbove
                    ? editorTop - dropdownHeight
                    : coords.bottom + window.scrollY;

                  Object.assign(component.style, {
                    top: `${dropdownTop}px`,
                    left: `${coords.left + window.scrollX}px`,
                  });
                }

                filteredItems?.forEach((item: any) => {
                  const button = document.createElement("button");
                  button.className =
                    "flex items-center px-4 py-2 text-left hover:bg-gray-100 w-full";

                  // Check if avatar_url exists, otherwise show initials
                  const avatarContainer = document.createElement("div");
                  avatarContainer.className =
                    "w-6 h-6 flex items-center justify-center rounded-md bg-primary-500 text-white text-xs font-bold mr-2";
                  avatarContainer.style.minWidth = "1.5rem";

                  if (item.profile_url && item.profile_url !== "") {
                    const img = document.createElement("img");
                    img.src = item.profile_url;
                    img.alt = item.name;
                    img.className = "w-full h-full rounded-md";
                    avatarContainer.appendChild(img);
                  } else {
                    avatarContainer.textContent = getInitials(
                      item?.name && item?.name !== " "
                        ? item?.name
                        : item?.email
                    );
                  }

                  const span = document.createElement("span");
                  span.textContent =
                    item?.name && item?.name !== " " ? item?.name : item?.email;
                  span.className = `${item.name ? "capitalize" : ""} text-xs font-semibold`;

                  button.appendChild(avatarContainer);
                  button.appendChild(span);

                  button.onclick = () => {
                    command({
                      id: item.id,
                      label:
                        item?.name && item?.name !== " "
                          ? item?.name
                          : item?.email,
                    });
                  };

                  component!.appendChild(button);
                });
              },

              onExit: () => {
                if (component) {
                  component.remove();
                  component = null;
                }

                // Clean up event listeners
                document.removeEventListener("keydown", handleKeyDown);
                document.removeEventListener("mousedown", handleClickOutside);
              },
            };
          },
        },
      }),
      Link.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: "https",
        HTMLAttributes: {
          class: "text-primary-500",
        },
      }),
      Code,
      CodeBlock.configure({
        HTMLAttributes: {
          class: "language-javascript",
        },
      }),
      ListItem,
    ],
    onUpdate: ({ editor }) => {
      setEditedMessage(editor.getHTML());
    },
  });

  // Centrifugo connection
  useEffect(() => {
    if (state?.thread) {
      const centrifugeClient: any = new Centrifuge(connectUrl, {
        getToken: getConnectionToken,
        debug: true,
      });

      const getPersonalChannelSubscriptionToken = async () => {
        return getSubscriptionToken(state?.thread?.thread_id);
      };

      // Create a subscription to the channel
      const sub = centrifugeClient.newSubscription(state?.thread?.thread_id, {
        getToken: getPersonalChannelSubscriptionToken,
      });

      sub.on("publication", (ctx: any) => {
        // console.log(ctx?.data)
        setMessages((prev: any) => [ctx.data, ...prev]);
      });

      centrifugeClient.connect();
      sub.subscribe();

      setSubscription(sub);

      return () => {
        sub.unsubscribe();
        centrifugeClient.disconnect();
        setSubscription(null);
      };
    }
  }, [state?.thread?.thread_id, connectUrl, state?.thread]);

  // fetch thread messages
  useEffect(() => {
    const token = localStorage.getItem("token") || "";

    // Reset the page number and messages when switching channels
    setPage(1);
    setMessages([]);
    setHasMore(true);

    if (state?.thread?.thread_id) {
      const getMessages = async () => {
        const res = await GetRequest(
          `/dms/thread/${state?.thread?.thread_id}/channels/${id2}?page=1&limit=15`,
          token
        );

        if (res?.status == 200 || res?.status == 201) {
          setMessages(res?.data?.data);
        }
      };
      getMessages();
    }
  }, [state?.thread?.thread_id, id]);

  // Handle message submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    const user = JSON.parse(localStorage.getItem("user") || "");
    const token = localStorage.getItem("token") || "";

    let content = editor?.getHTML();
    // content = removeHTMLTags(content);

    const strippedContent = content?.replace(/<[^>]+>/g, "").trim();

    // Check if content is empty or just whitespace
    if (!strippedContent) {
      return;
    }

    if (subscription) {
      editor?.commands?.clearContent();

      // payload
      const payload = {
        content: content,
        thread_id: state?.thread?.thread_id,
      };

      // save the data to the database
      await PostRequest(`/dms/messages/${id2}`, payload, token);
      dispatch({ type: ACTIONS.CALLBACK, payload: !state?.callback });
    }
  };

  const handleKeyDown = (event: any) => {
    if (event.key === "Enter") {
      if (event.shiftKey) {
        // Insert a new line when Shift + Enter is pressed
        event.preventDefault();
        editor?.commands.enter();
      } else {
        // Submit the form when Enter is pressed
        event.preventDefault();
        handleSubmit(event);
      }
    }
  };

  const fetchThreads = async (newPage: number = 1) => {
    const token = localStorage.getItem("token") || "";

    const res = await GetRequest(
      `/threads/${state?.thread?.thread_id}/channels/${id}?page=${newPage}&limit=10`,
      token
    );

    if (newPage === 1) {
      setMessages(res?.data?.data);
    } else {
      setMessages((prevThreads: any) => [
        ...prevThreads,
        ...(res?.data?.data ?? []),
      ]);
    }
    setPage(newPage);
    setHasMore(
      res?.data?.data.length > 0 && res?.data.pagination.previousPage !== null
    );
  };

  const fetchMoreData = () => {
    if (hasMore) {
      const nextPage = page + 1;
      fetchThreads(nextPage);
    }
  };

  // Close dropdown if click is outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        // setOpenDropdown(null);
      }
    };

    // Attach event listener
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      // Clean up the event listener
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Function to handle opening/closing the dropdown
  const handleDropdownToggle = (threadId: string) => {
    setOpenDropdown(openDropdown === threadId ? null : threadId);
  };

  // Function for editing the channel/thread
  const handleEdit = (threadId: string, message: string) => {
    setIsEdit(true);
    setThreadId(threadId);
    setEditedMessage(message);
    editor?.commands.setContent(message);
  };

  // Function for deleting the channel/thread
  const handleDelete = async () => {
    const token = localStorage.getItem("token") || "";

    setDeleteloading(true);

    const res = await DeleteRequest(
      `/dms/channels/${id}/messages/${threadId}`,
      token
    );
    if (res?.status === 200 || res?.status === 201) {
      dispatch({ type: ACTIONS.CALLBACK, payload: !state?.callback });
      setDeleteModal(false);
    }

    setDeleteloading(false);
  };

  const [isFocused, setIsFocused] = useState(true);

  useEffect(() => {
    setIsFocused(true);
    if (editor) {
      editor.commands.focus();
    }
  }, [editor]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [openDropdown]);

  const handleSubmitEditMessage = async (id: string) => {
    const token = localStorage.getItem("token") || "";

    if (editedMessage !== "<p></p>" && subscription) {
      editor?.commands?.clearContent();

      // payload
      const payload = {
        content: editedMessage,
        message_id: id,
        thread_id: uuid,
      };

      // save the data to the database
      await PutRequest(`/dms/messages/${id2}`, payload, token);
      setIsEdit(false);
    }
  };

  const handleView = (item: any) => {
    dispatch({ type: ACTIONS.THREAD, payload: item });
    dispatch({ type: ACTIONS.VISIBLE, payload: true });
  };

  //

  return (
    <div className="">
      <div className="flex items-center justify-between p-4 border-b py-6">
        <h1 className="text-[12px] font-bold text-[#1D2939]">Threads</h1>
        <div className="flex items-center gap-[11px] sticky top-0">
          <div
            className="cursor-pointer"
            onClick={() => dispatch({ type: ACTIONS.VISIBLE, payload: false })}
          >
            <svg
              width="15"
              height="15"
              viewBox="0 0 15 15"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.8536 2.85355C13.0488 2.65829 13.0488 2.34171 12.8536 2.14645C12.6583 1.95118 12.3417 1.95118 12.1464 2.14645L7.5 6.79289L2.85355 2.14645C2.65829 1.95118 2.34171 1.95118 2.14645 2.14645C1.95118 2.34171 1.95118 2.65829 2.14645 2.85355L6.79289 7.5L2.14645 12.1464C1.95118 12.3417 1.95118 12.6583 2.14645 12.8536C2.34171 13.0488 2.65829 13.0488 2.85355 12.8536L7.5 8.20711L12.1464 12.8536C12.3417 13.0488 12.6583 13.0488 12.8536 12.8536C13.0488 12.6583 13.0488 12.3417 12.8536 12.1464L8.20711 7.5L12.8536 2.85355Z"
                fill="currentColor"
                fill-rule="evenodd"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
        </div>
      </div>

      <div
        id="scrollableDiv"
        style={{
          height: state?.thread?.type === "thread" ? "80vh" : "93vh",
          overflow: "scroll",
          display: "flex",
          flexDirection: "column-reverse",
        }}
        className="pb-48 w-full"
      >
        <InfiniteScroll
          dataLength={messages?.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={
            messages?.length !== 0 &&
            messages?.length >= 15 && (
              <h4 className="my-5 text-xs text-center">Loading threads...</h4>
            )
          }
          style={{
            display: "flex",
            flexDirection: "column-reverse",
            overflow: "visible",
          }}
          scrollableTarget="scrollableDiv"
          inverse={true}
        >
          {messages?.map((item: any, index: number) => {
            const isCurrentUser = item.email === email;
            const nextMessage = messages[index + 1];
            const shouldShowAvatar =
              !nextMessage || nextMessage.email !== item.email;

            console.log();

            return (
              <Fragment key={index}>
                {isEdit && threadId === item?.id ? (
                  <div
                    key={index}
                    className={`relative group w-full cursor-pointer mb-5 py-2 bg-primary-100`}
                  >
                    <div className="relative group flex items-start gap-[7px] w-full px-2 py-3 hover:bg-[#F2F4F7] cursor-pointer">
                      <EditMessageEditor
                        handleKeyDown={handleKeyDown}
                        handleSubmit={() => handleSubmitEditMessage(item?.id)}
                        editor={editor}
                        setThreadId={setThreadId}
                        setIsEdit={setIsEdit}
                      />
                    </div>
                  </div>
                ) : (
                  <div
                    key={index}
                    className={`relative group flex items-start pl-1 gap-[12px] w-full cursor-pointer py-1 hover:bg-neutral-100`}
                  >
                    <div className="relative flex gap-3 w-full px-4 pb-0 rounded-lg ">
                      <div
                        className={`${!isCurrentUser && shouldShowAvatar ? "" : "invisible h-0"} border rounded-md size-10 flex items-center justify-center`}
                      >
                        {item?.avatar_url ? (
                          <div className="size-8 rounded-md border border-neutral-200 flex items-center justify-center overflow-hidden">
                            <Image
                              src={item?.avatar_url}
                              alt="user avatar"
                              width={50}
                              height={50}
                              className="rounded-md size-8"
                              style={{ objectFit: "cover" }}
                            />
                          </div>
                        ) : (
                          <div className="size-8 text-xs rounded-md border border-neutral-200 flex items-center justify-center bg-primary-500 text-white font-bold uppercase">
                            {item?.username?.charAt(0) ||
                              item?.email?.charAt(0)}
                            {item?.username?.charAt(1) ||
                              item?.email?.charAt(1)}
                          </div>
                        )}
                      </div>

                      {!isCurrentUser && !shouldShowAvatar && (
                        <p className="text-[#98A2B3] text-[9px] font-[400] absolute left-5 top-[3px] hidden group-hover:flex">
                          {moment(item?.created_at).format("LT")}
                        </p>
                      )}

                      <div
                        className={`w-full flex flex-col items-start ${!isCurrentUser && shouldShowAvatar ? "" : "-pt-4"}`}
                      >
                        {!isCurrentUser && shouldShowAvatar && (
                          <div className="flex gap-3 w-full items-center">
                            <h3 className="text-[13px] text-[#000000] font-[600] m-0">
                              {item?.username || item?.email}
                            </h3>
                            <p className="text-[#98A2B3] text-[11px] font-[400] mt-[3px]">
                              {moment(item?.created_at).format("LT")}
                            </p>
                          </div>
                        )}

                        <div className="flex items-center gap-2">
                          <div
                            style={{
                              whiteSpace: "pre-line",
                              wordBreak: "break-word",
                              overflowWrap: "break-word",
                            }}
                            className="text-[#344054] text-[13px] font-[400] whitespace-pre-wrap break-words"
                            dangerouslySetInnerHTML={{
                              __html: item.message
                                .replace(
                                  /<ul>/g,
                                  '<ul style="padding-left: 10px; list-style-type: disc; list-style-position: outside;">'
                                )
                                .replace(
                                  /<ol>/g,
                                  '<ol style="padding-left: 10px; list-style-type: decimal; list-style-position: outside;">'
                                )
                                .replace(
                                  /<li>/g,
                                  '<li style="margin-left: 18px;">'
                                ),
                            }}
                          />
                          <span className="text-[9px] text-neutral-500">
                            {item?.edited ? "(edited)" : ""}
                          </span>
                        </div>

                        <div className="w-full border-neutral-200 flex flex-col md:flex-row md:flex-wrap md:items-center justify-between gap-0">
                          <div className="flex items-center gap-1 mt-1">
                            <AvatarGroups
                              avatars={item?.messages?.map(
                                (message: any) => message?.avatar_url
                              )}
                            />

                            {item?.message_count >= 1 && (
                              <small
                                onClick={() => handleView(item)}
                                className="text-xs font-semibold text-[#5F5FE1] cursor-pointer"
                              >
                                {item?.message_count}{" "}
                                {item?.message_count > 1 ? "replies" : "reply"}
                              </small>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* hovered reply section */}

                    {/* <div className="flex items-center absolute -top-4 right-10 -mb-3 shadow-4xl border gap-4 justify-between bg-white rounded-md py-1 px-4 hidden group-hover:flex">
                      {user?.email === item?.email && (
                        <div
                          ref={buttonRef}
                          onClick={() =>
                            handleDropdownToggle(item?.thread_id)
                          }
                          className="text-xs cursor-pointer hover:bg-[#f2f4f7] p-1 rounded-sm"
                        >
                          <EllipsisVertical size={18} />
                        </div>
                      )}

                      {openDropdown === item?.thread_id && (
                        <div
                          className="absolute z-40 top-0 border overflow-hidden right-0 mt-2 bg-white shadow-lg rounded-md w-[200px]"
                          ref={dropdownRef}
                        >
                          <div
                            className="text-xs cursor-pointer hover:bg-[#f2f4f7] p-2 py-3 rounded-sm hover:bg-primary-500 hover:text-white"
                            onClick={() =>
                              handleEdit(item?.thread_id, item?.message)
                            }
                          >
                            Edit
                          </div>

                          <div
                            className="text-xs cursor-pointer hover:bg-[#f2f4f7] p-2 py-3rounded-sm hover:bg-primary-500 hover:text-white"
                            onClick={() => {
                              setDeleteModal(true),
                                setThreadId(item?.thread_id);
                            }}
                          >
                            Delete
                          </div>
                        </div>
                      )}
                    </div> */}
                  </div>
                )}
              </Fragment>
            );
          })}
        </InfiniteScroll>
      </div>

      <div className="absolute w-full bottom-0 left-0 right-0 bg-white">
        <div
          className={`py-[9.5px] px-[12.67px] mt-5 mb-3 border ${
            isFocused ? "border-primary-500" : "border-[#E4E7EC]"
          } rounded-md mx-3 z-10 bg-white`}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        >
          <Wysiwyg
            handleKeyDown={handleKeyDown}
            handleSubmit={handleSubmit}
            editor={editor}
          />
        </div>
      </div>

      {deleteModal && (
        <ConfirmModal
          title="Delete Message"
          content="Are you sure you want to delete this message"
          onConfirm={handleDelete}
          onCancel={() => setDeleteModal(false)}
          loading={deleteloading}
        />
      )}
    </div>
  );
};

export default Comments;
