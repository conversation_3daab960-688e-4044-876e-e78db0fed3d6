"use client";
import Image from "next/image";
import React, {
  FormEvent,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import { GetRequest, PostRequest } from "~/utils/request";
import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Mention from "@tiptap/extension-mention";
import Code from "@tiptap/extension-code";
import CodeBlock from "@tiptap/extension-code-block";
import Focus from "@tiptap/extension-focus";
import axios from "axios";
import { Centrifuge, Subscription } from "centrifuge";
import { useParams } from "next/navigation";
import { format } from "timeago.js";
import ListItem from "@tiptap/extension-list-item";
import Link from "@tiptap/extension-link";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import Wysiwyg from "../wysiwyg/Wysiwyg";
import InfiniteScroll from "react-infinite-scroll-component";

/* eslint-disable */

const Comments = () => {
  const [messages, setMessages] = useState<any>([]);
  const params = useParams();
  const id = params.id as string;
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [, setMentionQuery] = useState<string>("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [page, setPage] = useState(1);
  const pageSize = 10;
  const [loadmoreloading, setLoadmoreloading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const { state, dispatch } = useContext(DataContext);

  const connectUrl: any = process.env.NEXT_PUBLIC_CONNECT_URL;

  // Function to scroll to the bottom of the messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const data = [
    { title: "Event Name", text: state?.thread?.event_name },
    { title: "Action Type", text: state?.thread?.content },
    { title: "Status", text: state?.thread?.status },
  ];

  useEffect(() => {
    if (page === 1) {
      scrollToBottom();
    }
  }, [messages, page]);

  // Get connection token
  const getConnectionToken = async () => {
    const token = localStorage.getItem("token") || "";
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/connection`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  // Fetch subscription token
  const getSubscriptionToken = async (thread: string) => {
    const token = localStorage.getItem("token") || "";

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/subscription`,
      { channel: thread },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  // Dummy data for mentions
  const mentions = [
    { id: "1", label: "John Doe" },
    { id: "2", label: "Jane Smith" },
    { id: "3", label: "Alice Johnson" },
  ];

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          HTMLAttributes: {
            class: "list-disc",
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: "list-decimal",
          },
        },
      }),
      Placeholder.configure({
        placeholder: "Reply...",
      }),
      Mention.configure({
        HTMLAttributes: {
          class: "mention",
        },
        suggestion: {
          items: (query) => {
            const queryString = String(query || "");
            setMentionQuery(queryString);
            return mentions.filter((item) =>
              item.label.toLowerCase().includes(queryString.toLowerCase())
            );
          },
        },
      }),
      Link.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: "https",
      }),
      Focus.configure({
        className: "has-focus",
        mode: "all",
      }),
      Code,
      CodeBlock.configure({
        HTMLAttributes: {
          class: "language-javascript",
        },
      }),
      ListItem,
    ],
    autofocus: true,
    content: "",
  });

  // Centrifugo connection
  useEffect(() => {
    if (state?.thread) {
      const centrifugeClient: any = new Centrifuge(connectUrl, {
        getToken: getConnectionToken,
        debug: true,
      });

      const getPersonalChannelSubscriptionToken = async () => {
        return getSubscriptionToken(state?.thread?.thread_id);
      };

      // Create a subscription to the channel
      const sub = centrifugeClient.newSubscription(state?.thread?.thread_id, {
        getToken: getPersonalChannelSubscriptionToken,
      });

      sub.on("publication", (ctx: any) => {
        setMessages((prev: any) => [ctx.data, ...prev]);
      });

      centrifugeClient.connect();
      sub.subscribe();

      setSubscription(sub);

      return () => {
        sub.unsubscribe();
        centrifugeClient.disconnect();
        setSubscription(null);
      };
    }
  }, [state?.thread?.channels_id, connectUrl, state?.thread]);

  // fetch thread messages
  useEffect(() => {
    const token = localStorage.getItem("token") || "";
    setPage(1);
    setMessages([]);
    setHasMore(true);

    if (state?.thread?.channels_id) {
      const getMessages = async () => {
        const res = await GetRequest(
          `/threads/${state?.thread?.thread_id}/channels/${state?.thread?.channels_id}?page=${page}&limit=10`,
          token
        );

        if (res?.status == 200 || res?.status == 201) {
          setMessages(res?.data?.data);
        }
      };
      getMessages();
    }
  }, [state?.thread?.channels_id, state?.thread?.thread_id]);

  // Handle message submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    const user = JSON.parse(localStorage.getItem("user") || "");
    const token = localStorage.getItem("token") || "";

    let content = editor?.getHTML();

    if (content !== "<p></p>" && subscription) {
      editor?.commands?.clearContent();

      // payload
      const payload = {
        content: content,
        channels_id: state?.thread?.channels_id,
        user_id: user?.id,
        thread_id: state?.thread?.thread_id,
      };

      // save the data to the database
      await PostRequest(
        `/channels/${state?.thread?.channels_id}/messages`,
        payload,
        token
      );
    }
  };

  const handleKeyDown = (event: any) => {
    if (event.key === "Enter") {
      if (event.shiftKey) {
        // Insert a new line when Shift + Enter is pressed
        event.preventDefault();
        editor?.commands.enter();
      } else {
        // Submit the form when Enter is pressed
        event.preventDefault();
        handleSubmit(event);
      }
    }
  };

  const fetchThreads = async (newPage: number = 1) => {
    const token = localStorage.getItem("token") || "";

    const res = await GetRequest(
      `/threads/${state?.thread?.thread_id}/channels/${state?.thread?.channels_id}?page=${newPage}&limit=10`,
      token
    );

    if (newPage === 1) {
      setMessages(res?.data?.data);
    } else {
      setMessages((prevThreads: any) => [
        ...prevThreads,
        ...(res?.data?.data ?? []),
      ]);
    }
    setPage(newPage);
    setHasMore(
      res?.data?.data.length > 0 && res?.data.pagination.previousPage !== null
    );
  };

  const fetchMoreData = () => {
    if (hasMore) {
      const nextPage = page + 1;
      fetchThreads(nextPage);
    }
  };

  //

  return (
    <div className="">
      <div className="flex items-center justify-between p-4 border-b">
        <h1 className="text-[12px] font-bold text-[#1D2939]">Comments</h1>
        <div className="flex items-center gap-[11px] sticky top-0">
          <div
            className="cursor-pointer"
            onClick={() =>
              dispatch({ type: ACTIONS.SHOW_THREAD, payload: false })
            }
          >
            <svg
              width="15"
              height="15"
              viewBox="0 0 15 15"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.8536 2.85355C13.0488 2.65829 13.0488 2.34171 12.8536 2.14645C12.6583 1.95118 12.3417 1.95118 12.1464 2.14645L7.5 6.79289L2.85355 2.14645C2.65829 1.95118 2.34171 1.95118 2.14645 2.14645C1.95118 2.34171 1.95118 2.65829 2.14645 2.85355L6.79289 7.5L2.14645 12.1464C1.95118 12.3417 1.95118 12.6583 2.14645 12.8536C2.34171 13.0488 2.65829 13.0488 2.85355 12.8536L7.5 8.20711L12.1464 12.8536C12.3417 13.0488 12.6583 13.0488 12.8536 12.8536C13.0488 12.6583 13.0488 12.3417 12.8536 12.1464L8.20711 7.5L12.8536 2.85355Z"
                fill="currentColor"
                fill-rule="evenodd"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
        </div>
      </div>

      {state?.thread?.type === "thread" && (
        <div className="flex sticky flex-col gap-[26px] pt-3 px-5">
          <tbody className="text-[10px] leading-3 text-neutral-500 text-left">
            {data?.map((item: any) => (
              <tr
                key={item.title}
                className="[&:not(:first-child)]:[--t:6px] [&:not(:last-child)]:[--b:6px] text-xs"
              >
                <th
                  scope="row"
                  className="p-0 pb-[var(--b)] pt-[var(--t)] font-normal align-top text-xs"
                >
                  {item.title}
                </th>
                <th className="p-0 pl-7  pb-[var(--b)] pt-[var(--t)] font-bold align-top text-xs">
                  {item.text}
                </th>
              </tr>
            ))}
          </tbody>
          <hr />
        </div>
      )}

      <div
        id="scrollableDivs"
        style={{
          height: state?.thread?.type === "thread" ? "80vh" : "93vh",
          overflow: "scroll",
          display: "flex",
          flexDirection: "column-reverse",
        }}
        className="pb-40 w-full"
      >
        <InfiniteScroll
          dataLength={messages?.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={
            messages?.length !== 0 &&
            messages?.length >= 15 && (
              <h4 className="my-5 text-xs text-center">Loading threads...</h4>
            )
          }
          style={{
            display: "flex",
            flexDirection: "column-reverse",
            overflow: "visible",
          }}
          scrollableTarget="scrollableDivs"
          inverse={true}
        >
          {messages?.map((item: any, index: number) => (
            <div
              key={index}
              className="flex items-start gap-[7px] w-full p-4 py-3 hover:bg-[#F2F4F7] cursor-pointer"
            >
              {item?.avatar_url ? (
                <div className="size-10 rounded-md border border-neutral-200 flex items-center justify-center overflow-hidden">
                  <Image
                    src={item?.avatar_url}
                    alt="user avatar"
                    width={50}
                    height={50}
                    className="rounded-md size-10"
                    style={{ objectFit: "cover" }}
                  />
                </div>
              ) : (
                <div className="size-10 rounded-md border border-neutral-200 flex items-center justify-center bg-primary-500 text-white font-bold uppercase">
                  {item?.username?.charAt(0) || item?.email?.charAt(0)}
                  {item?.username?.charAt(1) || item?.email?.charAt(1)}
                </div>
              )}

              <div className="w-full flex flex-col items-start">
                <div className="flex gap-4 w-full items-center">
                  <h1 className="text-[13px] text-[#000000] font-[600]">
                    {item?.username || item?.email}
                  </h1>

                  <p className="text-[#98A2B3] text-[11px] font-[400]">
                    {format(item?.created_at)}
                  </p>
                </div>

                <div
                  className="text-[#344054] text-[13px] font-[400] leading-relaxed"
                  dangerouslySetInnerHTML={{
                    __html: item.message
                      .replace(
                        /<ul>/g,
                        '<ul style="padding-left: 10px; list-style-type: disc; list-style-position: outside;">'
                      )
                      .replace(
                        /<ol>/g,
                        '<ol style="padding-left: 10px; list-style-type: decimal; list-style-position: outside;">'
                      )
                      .replace(/<li>/g, '<li style="margin-left: 18px;">'),
                  }}
                />
              </div>
            </div>
          ))}
        </InfiniteScroll>
      </div>

      <div className="absolute w-full bottom-0 left-0 right-0 bg-white">
        <div
          className={`py-[9.5px] px-[12.67px] mt-5 mb-3 border border-[#E4E7EC] rounded-md focus-within:border-[blue] mx-3 z-10 bg-white`}
        >
          <Wysiwyg
            handleKeyDown={handleKeyDown}
            handleSubmit={handleSubmit}
            editor={editor}
          />
        </div>
      </div>
    </div>
  );
};

export default Comments;
