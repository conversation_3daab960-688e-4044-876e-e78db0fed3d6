"use client";

// import cogoToast from "cogo-toast";
import { useParams, useSearchParams } from "next/navigation";
import { X } from "lucide-react";
import React, { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Button } from "~/components/ui/button";
import Loading from "~/components/ui/loading";
import { DeleteRequest, GetRequest, PutRequest } from "~/utils/request";
import cogoToast from "cogo-toast";
import ConfirmModal from "~/telexComponents/ConfirmModal";

interface Field {
  label: string;
  description?: string;
  type:
    | "text"
    | "checkbox"
    | "number"
    | "dropdown"
    | "multi-checkbox"
    | "radio"
    | "multi-select"; // Added radio type
  required: boolean;
  default?: string | number | boolean | string[];
  options?: string[];
}

/* eslint-disable */

export default function CustomSettings({ isSystem, isActive }: any) {
  const [settings, setSettings] = useState<Field[]>([]);
  const [data, setData] = useState<Record<string, any>>({});
  const [multiSelectValues, setMultiSelectValues] = useState<
    Record<string, string[]>
  >({});
  const [textInputs, setTextInputs] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const searchParams = useSearchParams();
  const [buttonloading, setButtonloading] = useState(false);
  const params = useParams();
  const id = params.id as string;
  const [deleteloading, setDeleteloading] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem("token") || "";
    const orgId = localStorage.getItem("orgId") || "";

    const getSettings = async () => {
      const res = await GetRequest(
        `/organisations/${orgId}/integrations/custom/${id}/settings`,
        token
      );
      if (res?.status === 200 || res?.status === 201) {
        const integrationSettings = res?.data?.data.settings;

        if (
          Array.isArray(integrationSettings) &&
          integrationSettings.length > 0
        ) {
          setSettings(integrationSettings);

          // Populate the default values based on field type
          setData(
            integrationSettings.reduce(
              (acc: Record<string, any>, field: Field) => {
                const defaultValue =
                  field.type === "multi-checkbox" &&
                  Array.isArray(field.default)
                    ? field.default
                    : field.default || "";

                return { ...acc, [field.label]: defaultValue };
              },
              {}
            )
          );

          setMultiSelectValues(
            integrationSettings.reduce(
              (acc: Record<string, string[]>, field: Field) => {
                return {
                  ...acc,
                  [field.label]:
                    field.type === "multi-select" &&
                    Array.isArray(field.default)
                      ? field.default
                      : typeof field.default === "string"
                        ? field.default.split(",").map((v) => v.trim())
                        : [],
                };
              },
              {}
            )
          );
        }
      }
      setLoading(false);
    };

    if (id) {
      getSettings();
    }
  }, [id]);

  const handleMultiSelectInput = (
    label: string,
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const value = event.target.value;
    setTextInputs((prev) => ({ ...prev, [label]: value }));

    if (value.includes("\n") || value.includes(",")) {
      const newValues = value
        .split(/[\n,]/)
        .map((item) => item.trim())
        .filter((item) => item && !multiSelectValues[label]?.includes(item));

      setMultiSelectValues((prevValues) => ({
        ...prevValues,
        [label]: [...(prevValues[label] || []), ...newValues],
      }));

      setTextInputs((prev) => ({ ...prev, [label]: "" }));
      setData((prevData) => ({
        ...prevData,
        [label]: [...(multiSelectValues[label] || []), ...newValues],
      }));
    }
  };

  const handleRemoveMultiSelectValue = (
    label: string,
    valueToRemove: string
  ) => {
    setMultiSelectValues((prevValues) => {
      const updatedValues =
        prevValues[label]?.filter((item) => item !== valueToRemove) || [];
      // Update `data` state accordingly
      setData((prevData) => ({ ...prevData, [label]: updatedValues }));
      return { ...prevValues, [label]: updatedValues };
    });
  };

  // Handle paste for multi-select
  const handlePasteMultiSelect = (
    label: string,
    e: React.ClipboardEvent<HTMLTextAreaElement>
  ) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text");
    const newValues = pastedText
      .split(/[\n,]/)
      .map((item) => item.trim())
      .filter((item) => item && !multiSelectValues[label]?.includes(item));

    setMultiSelectValues((prevValues) => ({
      ...prevValues,
      [label]: [...(prevValues[label] || []), ...newValues],
    }));
    setData((prevData) => ({
      ...prevData,
      [label]: [...(data[label] || []), ...newValues],
    }));
  };

  const saveSettings = async () => {
    setButtonloading(true);

    try {
      // Update settings with current data values
      const updatedSettings = settings.map((field) => ({
        ...field,
        default: data[field.label],
      }));

      // Create a new integration object
      const payload = {
        setting_entry: {
          settings: updatedSettings,
        },
      };

      const token = localStorage.getItem("token") || "";
      const orgId = localStorage.getItem("orgId") || "";

      const res = await PutRequest(
        `/organisations/${orgId}/integrations/custom/${id}/settings`,
        payload,
        token
      );
      if (res?.status === 200 || res?.status === 201) {
        cogoToast.success(res?.data?.message);
      }
    } catch (error) {
      console.error("Error saving settings:", error);
    } finally {
      setButtonloading(false);
    }
  };

  const handleChange = (label: string, value: any) => {
    setData((prevData) => ({
      ...prevData,
      [label]: value,
    }));
  };

  // delete integration
  const deleteIntegration = async () => {
    const token = localStorage.getItem("token") || "";
    const orgId = localStorage.getItem("orgId") || "";

    setDeleteloading(true);

    const res = await DeleteRequest(
      `/organisations/${orgId}/integrations/custom/${id}`,
      token
    );
    if (res?.status === 200 || res?.status === 201) {
      cogoToast.success(res?.data?.message);
      window.location.href = "/dashboard/applications";
    }
    setDeleteloading(false);
  };

  //

  return (
    <div className="text-neutral-600 pb-4">
      <div className="mb-6">
        <h2 className="text-lg font-semibold">Settings</h2>
        <p className="text-[#5C5C5C] mt-2 text-sm">
          Configure your integration to allow you to complete an action with an
          app simply by sending a message in Telex.
        </p>
      </div>

      {isSystem && !isActive ? (
        <div className="text-center text-gray-400 text-sm mt-20">
          Integration is not activated yet <br /> Activate to view Settings
        </div>
      ) : (
        <>
          {loading ? (
            <div className="flex flex-col justify-center mt-10">
              <Loading height="30px" width="30px" color="blue" />
            </div>
          ) : (
            <>
              {settings?.map((field) => (
                <div key={field.label} className="mb-5">
                  {/* <label className="text-sm mb-1 block capitalize">
                  {field.label}
                </label> */}

                  {field.type === "text" && (
                    <>
                      <label className="text-sm mb-1 block capitalize">
                        {field.label}
                      </label>
                      <input
                        type="text"
                        value={data[field.label] || ""}
                        onChange={(e) =>
                          handleChange(field.label, e.target.value)
                        }
                        className="w-full md:w-[500px] py-3 text-xs text-[#747474] px-4 border rounded-sm border-[#D0D0FD] outline-none focus:border-primary-500"
                      />
                    </>
                  )}

                  {field.type === "checkbox" && (
                    <div className="flex items-center gap-2 my-8">
                      <input
                        className="size-4"
                        type="checkbox"
                        checked={data[field.label] || false}
                        onChange={(e) =>
                          handleChange(field.label, e.target.checked)
                        }
                      />
                      <label className="text-sm capitalize">
                        {field.label}
                      </label>
                    </div>
                  )}
                  {field.type === "number" && (
                    <>
                      <label className="text-sm mb-1 block capitalize">
                        {field.label}
                      </label>
                      <input
                        type="number"
                        value={data[field.label] || ""}
                        onChange={(e) =>
                          handleChange(field.label, Number(e.target.value))
                        }
                        className="w-full md:w-[500px] py-3 text-xs text-[#747474] px-4 border rounded-sm border-[#D0D0FD] outline-none focus:border-primary-500"
                      />
                    </>
                  )}
                  {field.type === "dropdown" && (
                    <>
                      <label className="text-sm mb-1 block capitalize">
                        {field.label}
                      </label>
                      <Select
                        value={data[field.label] || ""}
                        onValueChange={(value) =>
                          handleChange(field.label, value)
                        }
                      >
                        <SelectTrigger className="h-10 w-full md:w-[500px] text-primary focus:outline-none focus:ring-0 focus:ring-primary focus-visible:ring-0 focus-visible:ring-primary">
                          <SelectValue placeholder="" />
                        </SelectTrigger>
                        <SelectContent>
                          {field?.options?.map((option, index: number) => (
                            <SelectItem value={option} key={index}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </>
                  )}

                  {field.type === "multi-checkbox" && (
                    <>
                      <label className="text-sm mb-1 block capitalize">
                        {field.label}
                      </label>
                      <div>
                        {field.options?.map((option) => (
                          <label
                            key={option}
                            className="text-sm mb-1 flex items-center gap-2"
                          >
                            <input
                              className=""
                              type="checkbox"
                              checked={
                                data[field.label]?.includes(option) || false
                              }
                              onChange={(e) => {
                                const selected = data[field.label] || [];
                                const updated = e.target.checked
                                  ? [...selected, option]
                                  : selected.filter(
                                      (opt: string) => opt !== option
                                    );
                                handleChange(field.label, updated);
                              }}
                            />
                            {option}
                          </label>
                        ))}
                      </div>
                    </>
                  )}

                  {field.type === "radio" && (
                    <>
                      <label className="text-sm mb-1 block capitalize">
                        {field.label}
                      </label>
                      <div>
                        {field.options?.map((option) => (
                          <label
                            key={option}
                            className="text-sm mb-1 flex items-center gap-2"
                          >
                            <input
                              className="size-4"
                              type="radio"
                              name={field.label}
                              value={option}
                              checked={data[field.label] === option}
                              onChange={(e) =>
                                handleChange(field.label, e.target.value)
                              }
                            />
                            {option}
                          </label>
                        ))}
                      </div>
                    </>
                  )}

                  {field.type === "multi-select" && (
                    <>
                      <label className="text-sm mb-1 block capitalize">
                        {field.label}
                      </label>
                      <div className="w-full md:w-[600px] border-[1.5px] border-[#D0D0FD] rounded max-h-[150px] px-[16px] py-[12px] overflow-auto flex flex-row space-x-2 flex-wrap">
                        {multiSelectValues[field.label]?.map((value, index) => (
                          <div key={index} className="h-full">
                            <div className="text-[14px] font-[500] mb-1 flex items-center px-2 py-1 bg-[#F2F4F7] rounded">
                              <span>{value}</span>
                              <button
                                title="removeValue"
                                type="button"
                                onClick={() =>
                                  handleRemoveMultiSelectValue(
                                    field.label,
                                    value
                                  )
                                }
                                className="ml-3"
                              >
                                <X width={15} height={15} />
                              </button>
                            </div>
                          </div>
                        ))}
                        <textarea
                          id={`textarea-${field.label}`}
                          value={textInputs[field.label] || ""}
                          onPaste={(e) =>
                            handlePasteMultiSelect(field.label, e)
                          }
                          onChange={(e) =>
                            handleMultiSelectInput(field.label, e)
                          }
                          placeholder="Type values and press Enter or paste them here..."
                          rows={1}
                          className="text-[14px] inline-block h-[25px] w-full focus:outline-none resize-none"
                        />
                      </div>
                    </>
                  )}
                </div>
              ))}

              <div className="my-10 flex flex-col sm:flex-row gap-7">
                <Button
                  onClick={saveSettings}
                  className="w-[200px] px-6 py-6 bg-primary-500 hover:bg-opacity-80 disabled:cursor-not-allowed disabled:bg-opacity-50 text-white font-medium"
                >
                  {buttonloading ? (
                    <span className="flex items-center gap-x-2">
                      <span className="animate-pulse">Saving...</span>{" "}
                      <Loading width="20" height="40" />
                    </span>
                  ) : (
                    "Save Settings"
                  )}
                </Button>

                {!loading && !isSystem && (
                  <Button
                    onClick={() => setDeleteModal(true)}
                    className="w-[200px] px-6 py-6 bg-transparent border border-red-500 text-red-500 hover:bg-opacity-80 disabled:cursor-not-allowed disabled:bg-opacity-50 font-medium"
                  >
                    Delete Integration
                  </Button>
                )}
              </div>
            </>
          )}
        </>
      )}

      {deleteModal && (
        <ConfirmModal
          content="Are you sure you want to remove this integration"
          onConfirm={deleteIntegration}
          onCancel={() => setDeleteModal(false)}
          loading={deleteloading}
        />
      )}
    </div>
  );
}
