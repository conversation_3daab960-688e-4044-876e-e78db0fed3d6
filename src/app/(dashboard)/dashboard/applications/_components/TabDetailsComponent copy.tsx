import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "~/components/ui/tabs";
import { useContext, useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { LinkIcon } from "lucide-react";
import Loading from "~/components/ui/loading";
import Commands from "./Commands";
import Output from "./Output";
import {
  IntegrationGetRequest,
  GetRequest,
  PatchRequest,
} from "~/utils/request";
import cogoToast from "cogo-toast";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import CustomSettings from "./CustomSettings";

//

/* eslint-disable */

function TabDetailsComponent({ app, loading }: any) {
  const [buttonloading, setButtonloading] = useState(false);
  const params = useParams();
  const id = params.id as string;
  const { state, dispatch } = useContext(DataContext);
  const [statusloading, setStatusloading] = useState(true);
  const [integration, setIntegration] = useState<any>(null);
  const [callback, setCallback] = useState(false);
  const [integrationChannels, setIntegrationChannels] = useState<any>(null);
  const [configuredChannels, setConfiguredChannels] = useState("all");
  const [slashCommands, setSlashCommands] = useState<any>([]);
  const [mappings, setMappings] = useState<any>(null);
  const [slackChannels, setSlackChannels] = useState<any>([]);
  const [appName, setAppName] = useState("");
  const [isActive, setIsActive] = useState(false);
  const [isSystem, setIsSystem] = useState(false);

  const getStatus = async () => {
    const orgId = localStorage.getItem("orgId") || "";
    const token = localStorage.getItem("token") || "";

    const res = await GetRequest(
      `/organisations/${orgId}/integrations/custom/${id}/status`,
      token
    );

    if (res?.status === 200 || res?.status === 201) {
      setIsActive(res?.data?.data?.is_active);
      setIsSystem(res?.data?.data?.is_system);
    }
    setStatusloading(false);
  };

  // get the status of an integration
  useEffect(() => {
    const data = localStorage.getItem("appName") || "";
    setAppName(data);

    getStatus();
  }, [callback]);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data === "integration-success") {
        getStatus();
        cogoToast.success("Integration app status set successfully");
      }
    };

    window.addEventListener("message", handleMessage, false);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  // fetch integration channels for output
  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";
    const token = localStorage.getItem("token") || "";

    if (id) {
      const getIntegrationsChannels = async () => {
        const res = await GetRequest(
          `/organisations/${orgId}/integrations/${id}/channels`,
          token
        );

        if (res?.status === 200 || res?.status === 201) {
          setIntegrationChannels(res?.data?.data?.channels);
          setConfiguredChannels(
            res?.data?.data?.is_allchannels ? "all" : "custom"
          );
        }
      };

      getIntegrationsChannels();
    }
  }, [id, callback]);

  // get slash commands
  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";
    const token = localStorage.getItem("token") || "";

    const getCommands = async () => {
      const res = await GetRequest(
        `/organisations/${orgId}/integrations/${id}/slash-commands`,
        token
      );
      if (res?.status === 200 || res?.status === 201) {
        setSlashCommands(res?.data?.data);
      }
    };

    if (app?.descriptions?.app_name === "Slack") {
      getCommands();
    }
  }, [id, state?.appCallback]);

  // get slack channels
  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";
    const token = localStorage.getItem("token") || "";

    const getSlackChannels = async () => {
      const res = await IntegrationGetRequest(
        `/slack/channels?organisation_id=${orgId}`,
        token
      );

      if (res?.status === 200 || res?.status === 201) {
        setSlackChannels(res?.data?.data);
      }
    };
    if (app?.descriptions?.app_name === "Slack") {
      getSlackChannels();
    }
  }, [state?.appCallback]);

  // output for slack -- mapping section
  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";
    const token = localStorage.getItem("token") || "";

    const getMappings = async () => {
      const res = await IntegrationGetRequest(
        `/slack/organisations/channels-mapping/${orgId}`,
        token
      );
      if (res?.status === 200 || res?.status === 201) {
        setMappings(res?.data?.data);
      }
    };
    if (app?.descriptions?.app_name === "Slack") {
      getMappings();
    }
  }, [state?.appCallback]);

  // connect app
  const connectApp = async () => {
    const orgId = localStorage.getItem("orgId") || "";
    const token = localStorage.getItem("token") || "";
    const active = localStorage.getItem("isActive") || "";
    localStorage.setItem("integrationId", id);

    if (app?.integration_type === "output" && !app?.is_active) {
      // slackIntegration();
      window.open(app?.auth_initiate_url, "_blank");
    } else {
      setButtonloading(true);

      const payload = {
        integration_id: id,
        status: isActive ? false : true,
      };

      const res = await PatchRequest(
        `/organisations/${orgId}/integrations/change_status`,
        payload,
        token
      );

      if (res?.status === 200 || res?.status === 201) {
        localStorage.setItem("isActive", active === "true" ? "false" : "true");
        setCallback(!callback);
        dispatch({ type: ACTIONS.APP_CALLBACK, payload: !state?.appCallback });
        cogoToast.success(res?.data?.message);
        setButtonloading(false);
      } else {
        setButtonloading(false);
      }
    }
  };

  // slack integration
  const slackIntegration = () => {
    const clientId = process.env.NEXT_PUBLIC_SLACK_CLIENT_ID;
    const redirectUri = encodeURIComponent(
      process.env.NEXT_PUBLIC_REDIRECT_URI || ""
    );

    const scope = encodeURIComponent(
      "chat:write channels:read groups:read chat:write.customize chat:write.public users:read"
    );

    // Get the screen width and height
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;

    // Popup dimensions
    const popupWidth = 600;
    const popupHeight = 600;

    // Calculate the position to center the popup
    const left = (screenWidth - popupWidth) / 2;
    const top = (screenHeight - popupHeight) / 2;

    // Correctly open the new window with proper syntax
    window.open(
      `https://slack.com/oauth/v2/authorize?client_id=${clientId}&scope=${scope}&redirect_uri=${redirectUri}`,
      "SlackAuthWindow",
      `width=${popupWidth},height=${popupHeight},top=${top},left=${left},resizable,scrollbars`
    );
  };

  //

  return (
    <div className="w-full">
      <Tabs defaultValue="description" className="w-full">
        {!loading && (
          <TabsList>
            <TabsTrigger value="description">Descriptions</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            {app?.descriptions?.app_name === "Slack" && (
              <TabsTrigger value="commands">Commands</TabsTrigger>
            )}
            <TabsTrigger value="output">Output</TabsTrigger>
          </TabsList>
        )}

        <hr className="my-[29px]" />

        {loading ? (
          <div className="flex items-center justify-center mt-20">
            <Loading width="40" height="40" color="blue" />
          </div>
        ) : (
          <>
            {app?.length === 0 ? (
              <div className="flex items-center justify-center mt-20">
                Integration not available
              </div>
            ) : (
              <>
                <TabsContent value="description" className="">
                  <div className="flex flex-col md:flex-row gap-8 py-6 md:py-0 md:pb-20 md:border-b-[1px]">
                    <div className="flex flex-col gap-3 items-start md:items-center">
                      <div className="w-[120px] h-[120px] md:w-[200px]  md:h-[200px] mb-4">
                        <Image
                          src={app?.descriptions?.app_logo}
                          width={100}
                          height={100}
                          alt="app logo"
                          className="h-full w-full"
                          unoptimized
                        />
                      </div>

                      {!statusloading && (
                        <Button
                          onClick={connectApp}
                          className="w-max rounded-[0.25rem] border-[1px] px-4 py-2 text-primary-500 text-sm font-semibold border-primary-500"
                        >
                          {buttonloading ? (
                            <span className="flex items-center gap-x-2">
                              <span className="animate-pulse">Loading...</span>{" "}
                              <Loading width="20" height="20" color="blue" />
                            </span>
                          ) : (
                            <>{isActive ? "Disconnect App" : "Connect App"}</>
                          )}
                        </Button>
                      )}
                    </div>

                    <div className="w-full flex flex-col gap-4 md:pt-1">
                      <div className="w-full flex flex-col gap-3">
                        <div className="w-full flex flex-col gap-2">
                          <h1 className="text-neutral-700 text-2xl md:text-4xl font-semibold">
                            {app?.descriptions?.app_name}
                          </h1>
                          <Link
                            href={app?.descriptions?.app_url}
                            className="flex items-center text-neutral-600 text-base gap-[0.2rem] underline pb-2"
                            passHref={true}
                            target="_blank"
                          >
                            {app?.descriptions?.app_url}
                            <LinkIcon width={20} height={20} />
                          </Link>

                          <p className="w-fit max-w-[800px] text-neutral-600 text-base leading-6 text-sm mb-3">
                            {app?.descriptions?.app_description}
                          </p>
                        </div>

                        <div className="flex flex-col gap-1">
                          <p className="w-fit max-w-[800px] text-neutral-600 text-base leading-6 text-sm mb-3">
                            <span className="text-neutral-700 text-base font-semibold">
                              Connected since:{" "}
                            </span>
                            {app?.date?.created_at}
                          </p>
                          <p className="w-fit max-w-[800px] text-neutral-600 text-base leading-6 text-sm mb-3">
                            <span className="text-neutral-700 text-base font-semibold">
                              Last synced:{" "}
                            </span>
                            {app?.date?.updated_at}
                          </p>
                        </div>
                      </div>

                      <div className="w-full flex justify-start items-center gap-4">
                        <button className="w-fit rounded-[.5rem] py-2 px-3 bg-neutral-200 text-neutral-500 text-sm">
                          Customer Support
                        </button>
                        <button className="w-fit rounded-[.5rem] py-2 px-3 bg-neutral-200 text-neutral-500 text-sm">
                          Communication
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="w-full flex flex-col gap-4 py-10">
                    <h1 className="text-neutral-700 text-lg font-medium leading-6">
                      {app?.descriptions?.app_name} events monitored on telex
                    </h1>
                    <ul className="list-disc px-6">
                      {app?.key_features?.map((item: any, index: number) => {
                        return (
                          <li
                            key={index}
                            className="w-fit max-w-[800px] text-neutral-600 text-base leading-6 text-sm mb-3"
                          >
                            {item}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </TabsContent>

                <TabsContent value="settings" className="">
                  <CustomSettings isSystem={isSystem} isActive={isActive} />
                </TabsContent>

                <TabsContent value="commands" className="">
                  <Commands app={app} slashCommands={slashCommands} />
                </TabsContent>

                <TabsContent value="output" className="">
                  <Output
                    integrationChannels={integrationChannels}
                    setIntegrationChannels={setIntegrationChannels}
                    configuredChannels={configuredChannels}
                    setConfiguredChannels={setConfiguredChannels}
                    app_name={app?.descriptions?.app_name}
                    mappings={mappings}
                    slackChannels={slackChannels}
                    isSystem={isSystem}
                    isActive={isActive}
                  />
                </TabsContent>
              </>
            )}
          </>
        )}
      </Tabs>
    </div>
  );
}

export default TabDetailsComponent;
