"use client";
import Link from "next/link";
import React from "react";
import { useState, useContext } from "react";
import { Button } from "~/components/ui/button";
import Loading from "~/components/ui/loading";
import { ACTIONS } from "~/store/Actions";
import { DataContext } from "~/store/GlobalState";
import { PostRequest } from "~/utils/request";

interface Props {
  hide: () => void;
}

export const AddIntegrationModal = ({ hide }: Props) => {
  const [url, setUrl] = useState<string>("");
  const [createloading, setCreateloading] = useState(false);
  const { state, dispatch } = useContext(DataContext);

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    const token = localStorage.getItem("token") || "";
    const orgId = localStorage.getItem("orgId") || "";

    // const validate = validateUrl(url);
    // if (!validate) return;

    setCreateloading(true);

    const payload = {
      json_url: url,
    };

    const res = await PostRequest(
      `/organisations/${orgId}/integrations/custom`,
      payload,
      token
    );
    if (res?.status === 200 || res?.status === 201) {
      dispatch({ type: ACTIONS.CALLBACK, payload: !state?.callback });

      setTimeout(() => {
        hide();
      }, 1000);
    }
    setCreateloading(false);
  };

  //

  return (
    <div className="w-screen z-50 h-screen fixed top-0 right-0 left-0 flex items-center justify-center .">
      <div
        className="w-full h-full absolute bg-black opacity-20"
        onClick={() => hide()}
      ></div>
      <div className="bg-white w-[382px] lg:w-[450px] flex flex-col rounded-lg z-10 p-5 gap-6">
        <div>
          <div className="w-full flex items-center justify-between mb-5">
            <h1 className="text-[#1D2939] lg:font-bold text-xl">
              Add New Integration
            </h1>
            <button onClick={() => hide()}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="25"
                viewBox="0 0 24 25"
                fill="none"
              >
                <path
                  d="M19.2806 18.7193C19.3503 18.789 19.4056 18.8717 19.4433 18.9628C19.481 19.0538 19.5004 19.1514 19.5004 19.2499C19.5004 19.3485 19.481 19.4461 19.4433 19.5371C19.4056 19.6281 19.3503 19.7109 19.2806 19.7806C19.2109 19.8502 19.1282 19.9055 19.0372 19.9432C18.9461 19.9809 18.8485 20.0003 18.75 20.0003C18.6514 20.0003 18.5539 19.9809 18.4628 19.9432C18.3718 19.9055 18.289 19.8502 18.2194 19.7806L12 13.5602L5.78061 19.7806C5.63988 19.9213 5.44901 20.0003 5.24999 20.0003C5.05097 20.0003 4.8601 19.9213 4.71936 19.7806C4.57863 19.6398 4.49957 19.449 4.49957 19.2499C4.49957 19.0509 4.57863 18.86 4.71936 18.7193L10.9397 12.4999L4.71936 6.28055C4.57863 6.13982 4.49957 5.94895 4.49957 5.74993C4.49957 5.55091 4.57863 5.36003 4.71936 5.2193C4.8601 5.07857 5.05097 4.99951 5.24999 4.99951C5.44901 4.99951 5.63988 5.07857 5.78061 5.2193L12 11.4396L18.2194 5.2193C18.3601 5.07857 18.551 4.99951 18.75 4.99951C18.949 4.99951 19.1399 5.07857 19.2806 5.2193C19.4213 5.36003 19.5004 5.55091 19.5004 5.74993C19.5004 5.94895 19.4213 6.13982 19.2806 6.28055L13.0603 12.4999L19.2806 18.7193Z"
                  fill="#344054"
                />
              </svg>
            </button>
          </div>

          <p className="text-[#586283] text-sm">
            Bring your team together, Create yours and share information
          </p>
        </div>

        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between gap-7 mb-3">
            <div className="w-full">
              <label htmlFor="channel-name" className="text-sm mb-1 block">
                Integration Json Url
              </label>
              <input
                type="text"
                name="channel-name"
                id="channel-name"
                value={url}
                placeholder="https://my-integration.json"
                onChange={(e) => setUrl(e.target.value)}
                className="w-full py-3 text-xs text-[#747474] px-4 border rounded-sm border-[#D0D0FD] outline-none focus:border-primary-500"
              />

              <Link
                className="underline text-primary-500 text-xs"
                href="/dashboard/applications/generate-json"
              >
                Generate integration JSON
              </Link>
            </div>
          </div>
        </div>

        <div className="w-full flex justify-end">
          <div className="w-[160px]" onClick={handleSubmit}>
            <Button
              variant="default"
              className="w-full bg-[#7141F8] hover:bg-[#8760f8] text-white"
              disabled={createloading}
            >
              {createloading ? (
                <span className="flex items-center gap-x-2">
                  <span className="animate-pulse">Saving...</span>{" "}
                  <Loading width="20" height="20" />
                </span>
              ) : (
                <span>Save</span>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
