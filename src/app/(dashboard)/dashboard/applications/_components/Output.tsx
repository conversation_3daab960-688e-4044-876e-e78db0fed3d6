"use client";

import { useContext, useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  GetRequest,
  IntegrationDeleteRequest,
  IntegrationPostRequest,
  PostRequest,
} from "~/utils/request";
import Loading from "~/components/ui/loading";
import { Checkbox } from "~/components/ui/checkbox";
import { useParams } from "next/navigation";
import cogoToast from "cogo-toast";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { CircleXIcon, ArrowLeftRightIcon } from "lucide-react";
import ConfirmModal from "~/telexComponents/ConfirmModal";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";

/* eslint-disable */

export default function Output({
  integrationChannels,
  setIntegrationChannels,
  configuredChannels,
  setConfiguredChannels,
  app_name,
  mappings,
  slackChannels,
  isSystem,
  isActive,
}: any) {
  const [integrations, setIntegrations] = useState<any>([]);
  const [buttonloading, setButtonloading] = useState(false);
  const [dataId, setDataId] = useState("");
  const [open, setOpen] = useState(false);
  const params = useParams();
  const id = params.id as string;
  const [telexChannel, setTelexChannel] = useState<any>(null);
  const [slackChannel, setSlackChannel] = useState<any>(null);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const { state, dispatch } = useContext(DataContext);
  const [deleteloading, setDeleteloading] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [maploading, setMaploading] = useState(false);

  // Fetch integrations
  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";
    const token = localStorage.getItem("token") || "";

    const getIntegrations = async () => {
      try {
        const res = await GetRequest(
          `/organisations/${orgId}/integrations`,
          token
        );

        if (res?.status === 200 || res?.status === 201) {
          setIntegrations(res?.data?.data);
        }
      } catch (error) {
        console.error("Error fetching integrations:", error);
      }
    };

    getIntegrations();
    setButtonloading(false);
    setOpen(false);
  }, []);

  // Handle checkbox change
  const handleCheckboxChange = async (isChecked: boolean, index: number) => {
    setIntegrationChannels((prevChannels: any[]) =>
      prevChannels.map((channel, i) =>
        i === index ? { ...channel, is_active: isChecked } : channel
      )
    );
    const orgId = localStorage.getItem("orgId") || "";
    const token = localStorage.getItem("token") || "";
    const updatedChannel = integrationChannels[index];

    const payload = {
      status: isChecked,
    };

    const res = await PostRequest(
      `/organisations/${orgId}/integrations/${id}/channels/${updatedChannel?.channel_id}`,
      payload,
      token
    );

    if (res?.status === 200 || res?.status === 201) {
      cogoToast.success(res?.data?.message);
    } else {
      cogoToast.error("Failed to update channel status");
    }
  };

  // mapping for slack and telex
  const mapping = async () => {
    const orgId = localStorage.getItem("orgId") || "";
    const token = localStorage.getItem("token") || "";

    setMaploading(true);

    const payload = {
      telex_channel_id: telexChannel?.channel_id,
      telex_channel_name: telexChannel?.channel_name,
      slack_channel_id: slackChannel?.id,
      slack_channel_name: slackChannel?.name,
    };

    const res = await IntegrationPostRequest(
      `/slack/organisations/channels-mapping/${orgId}`,
      payload,
      token
    );
    if (res?.status === 200 || res?.status === 201) {
      dispatch({ type: ACTIONS.APP_CALLBACK, payload: !state?.appCallback });
      cogoToast.success(res?.data?.message);
      setMaploading(false);
    } else {
      setMaploading(false);
    }
  };

  // deactivate/delete mapping
  const handleDeactivate = async () => {
    const orgId = localStorage.getItem("orgId") || "";
    const token = localStorage.getItem("token") || "";

    setDeleteloading(true);

    const payload = {
      slack_channel_id: selectedItem?.slack_channel_id,
      telex_channel_id: selectedItem?.telex_channel_id,
    };

    const res = await IntegrationDeleteRequest(
      `/slack/organisations/channels-mapping/${orgId}`,
      payload,
      token
    );

    if (res?.status === 200 || res?.status === 201) {
      cogoToast.success(res?.data?.message);
      dispatch({ type: ACTIONS.APP_CALLBACK, payload: !state?.appCallback });
      setConfirmDelete(false);
      setDeleteloading(false);
    } else {
      setDeleteloading(false);
    }
  };

  //

  return (
    <div className="text-neutral-600 pb-4 mb-20">
      <div className="mb-6">
        <h2 className="text-lg font-semibold">Output</h2>
        <p className="text-[#5C5C5C] mt-2 text-sm">
          Configure your slack integration to allow you to complete an action
          with an app simply by sending a message in Telex.
        </p>
      </div>

      {!isActive ? (
        <div className="text-center text-gray-400 text-sm mt-20">
          Integration is not activated yet <br /> Activate to view Output
        </div>
      ) : (
        <>
          <div className="my-5">
            <label className="block mb-2 text-sm text-black">
              Configured Channels
            </label>

            <RadioGroup
              defaultValue="comfortable"
              onValueChange={(value) => setConfiguredChannels(value)}
              value={configuredChannels}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="all" id="r1" />
                <label htmlFor="r1" className="block text-sm text-black">
                  All Channels
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="custom" id="r2" />
                <label htmlFor="r2" className="block text-sm text-black">
                  Custom Channels
                </label>
              </div>
            </RadioGroup>
          </div>

          {configuredChannels === "custom" && integrationChannels && (
            <div className="space-y-4">
              <label className="block mb-2 text-sm text-black">
                Select Custom Channels
              </label>

              {integrationChannels?.map((item: any, index: number) => (
                <div
                  key={item.channel_id}
                  className="flex items-center space-x-2"
                >
                  <Checkbox
                    id={item.channel_id}
                    checked={item.is_active}
                    onCheckedChange={(isChecked: boolean) =>
                      handleCheckboxChange(isChecked, index)
                    }
                    className="size-5"
                  />
                  <label
                    htmlFor={item.channel_id}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {item.channel_name}
                  </label>
                </div>
              ))}
            </div>
          )}

          {/* slack output */}
          {app_name === "Slack" && (
            <div>
              <hr className="my-10" />
              <h3 className="font-medium mb-10">
                Connect your Telex channels with Slack channels
              </h3>
              <div className="flex items-end gap-5 mt-5">
                <div className="w-80">
                  <label className="block mb-2 text-sm text-black">
                    Select Telex Channels
                  </label>

                  <Select
                    onValueChange={(value) =>
                      setTelexChannel(JSON.parse(value))
                    }
                    value={
                      telexChannel ? JSON.stringify(telexChannel) : undefined
                    }
                  >
                    <SelectTrigger className="h-10 text-primary focus:outline-none focus:ring-0 focus:ring-primary focus-visible:ring-0 focus-visible:ring-primary">
                      <SelectValue placeholder="Telex channels" />
                    </SelectTrigger>
                    <SelectContent>
                      {integrationChannels?.map((item: any, index: number) => {
                        return (
                          <SelectItem value={JSON.stringify(item)} key={index}>
                            {item?.channel_name}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>

                <div className="w-80">
                  <label className="block mb-2 text-sm text-black">
                    Select Slack Channels
                  </label>

                  <Select
                    onValueChange={(value) =>
                      setSlackChannel(JSON.parse(value))
                    }
                    value={
                      slackChannel ? JSON.stringify(slackChannel) : undefined
                    }
                  >
                    <SelectTrigger className="h-10 text-primary focus:outline-none focus:ring-0 focus:ring-primary focus-visible:ring-0 focus-visible:ring-primary">
                      <SelectValue placeholder="Slack channels" />
                    </SelectTrigger>

                    <SelectContent>
                      {slackChannels?.map((item: any, index: number) => (
                        <SelectItem value={JSON.stringify(item)} key={index}>
                          {item?.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="">
                  <button
                    disabled={!telexChannel || !slackChannel}
                    onClick={mapping}
                    className="px-6 rounded-md bg-primary-500 hover:bg-opacity-80 text-white font-medium text-sm h-10 disabled:cursor-not-allowed disabled:bg-opacity-50 "
                  >
                    {maploading ? (
                      <span className="flex items-center gap-x-2">
                        <span className="animate-pulse">Loading</span>{" "}
                        <Loading width="20" height="40" />
                      </span>
                    ) : (
                      "Connect"
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}

          {app_name === "Slack" && mappings && mappings?.length !== 0 && (
            <div>
              <hr className="my-10" />

              <h3 className="font-medium mb-8">Connected Channels</h3>

              <table className="w-[500px]">
                <thead>
                  <tr>
                    <th className="text-left text-sm font-semibold text-primary-500 pb-3">
                      Telex Channel
                    </th>
                    <th className="text-left text-sm font-semibold text-primary-500 pb-3"></th>{" "}
                    {/* Arrow Column */}
                    <th className="text-left text-sm font-semibold text-primary-500 pb-3">
                      Slack Channel
                    </th>
                    <th className="text-left text-sm font-semibold text-primary-500 pb-3">
                      Actions
                    </th>
                  </tr>
                </thead>

                <tbody>
                  {mappings?.map((item: any, index: number) => (
                    <tr key={index}>
                      <td className="text-primary-500 text-sm pb-4">
                        <div className="rounded-lg py-2 px-10 text-center border border-primary-400">
                          {item?.telex_channel_name}
                        </div>
                      </td>

                      <td className="px-4 py-2 text-center pb-4">
                        <ArrowLeftRightIcon
                          size={20}
                          className="text-primary-500"
                        />
                      </td>

                      <td className="text-primary-500 text-sm pb-4">
                        <div className="rounded-lg py-2 px-10 text-center border border-primary-400">
                          {item?.slack_channel_name}
                        </div>
                      </td>

                      <td className="px-4 py-2 text-primary-500 text-sm pb-4">
                        <div className="flex items-center gap-2">
                          <CircleXIcon
                            color="red"
                            size={20}
                            className="cursor-pointer"
                            onClick={() => {
                              setConfirmDelete(true);
                              setSelectedItem(item);
                            }}
                          />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}

      {/* modal section */}
      {confirmDelete && (
        <ConfirmModal
          content="Are you sure you want to Deactivate this channels"
          onConfirm={handleDeactivate}
          onCancel={() => setConfirmDelete(false)}
          loading={deleteloading}
        />
      )}
    </div>
  );
}
