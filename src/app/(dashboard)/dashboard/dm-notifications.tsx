import React, { useEffect, useContext, useRef, useState } from "react";
import { DataContext } from "~/store/GlobalState";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { ACTIONS } from "~/store/Actions";
import axios from "axios";
import { Centrifuge, Subscription } from "centrifuge";
import Image from "next/image";
import { useParams } from "next/navigation";

/* eslint-disable */

const Notify = ({
  name,
  message,
  image,
}: {
  name: string;
  message: string;
  image: string;
}) => {
  return (
    <div>
      <div className="flex gap-2 items-center mb-1">
        {image ? (
          <Image
            src={image}
            alt=""
            width={20}
            height={20}
            className="rounded-full border border-primary-500 size-6"
          />
        ) : (
          <div className="size-6 rounded-full border border-neutral-200 text-[10px] flex items-center justify-center bg-primary-500 text-white font-bold uppercase">
            {name?.charAt(0)}
            {name?.charAt(1)}
          </div>
        )}
        <div className="font-medium text-xs capitalize">{name}</div>
      </div>

      <div
        className="text-xs"
        dangerouslySetInnerHTML={{
          __html: message,
        }}
      />
    </div>
  );
};

const DMNotifications = () => {
  const { state, dispatch } = useContext(DataContext);
  const audioPlayer = useRef<any>(null);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [notifications, setNotifications] = useState<any>(null);
  const params = useParams();
  const id2 = params.id2 as string;

  const connectUrl: any = process.env.NEXT_PUBLIC_CONNECT_URL;

  // get connection token
  const getConnectionToken = async () => {
    const token = localStorage.getItem("token") || "";
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/connection`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  //fetch subscription token
  const getSubscriptionToken = async (channel: string) => {
    const token = localStorage.getItem("token") || "";

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/subscription`,
      { channel },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.data.token;
  };

  // centrifugo connection for notification
  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user") || "");
    const orgId = localStorage.getItem("orgId") || "";

    // Initialize Centrifuge client
    const centrifugeClient: any = new Centrifuge(connectUrl, {
      getToken: getConnectionToken,
      debug: true,
    });

    centrifugeClient.on("connect", () => {
      console.log("Connected to Centrifuge");
    });

    centrifugeClient.on("disconnect", () => {
      console.log("Disconnected from Centrifuge");
    });

    // Function to get the token for the personal channel
    const getPersonalChannelSubscriptionToken = async () => {
      return getSubscriptionToken(user?.id);
    };

    // Create a subscription to the channel
    const sub = centrifugeClient.newSubscription(user?.id, {
      getToken: getPersonalChannelSubscriptionToken,
    });

    // message publishing
    sub.on("publication", (ctx: any) => {
      console.log(ctx?.data?.notification_type);
      if (ctx?.data?.notification_type !== "new_message") {
        dispatch({
          type: ACTIONS.NOTIFICATION_CALLBACK,
          payload: !state?.notificationCallback,
        });
      }
      // dispatch({ type: ACTIONS.NOTIFICATION_TYPE, payload: ctx?.data?.notification_type });
      const response = ctx?.data?.data;

      if (Object.keys(response).length > 0) {
        if (ctx?.data?.data?.user_id !== user?.id) {
          setNotifications(ctx?.data?.data);
          dispatch({ type: ACTIONS.NOTIFICATIONS, payload: ctx?.data?.data });

          const channelId = ctx?.data?.data?.channel_id;

          if (channelId) {
            let existingChannelIds: string[] = JSON.parse(
              localStorage.getItem("channelIds") || "[]"
            );

            // Add new channelId if it doesn't already exist in the array
            if (!existingChannelIds.includes(channelId)) {
              existingChannelIds.push(channelId);
              localStorage.setItem(
                "channelIds",
                JSON.stringify(existingChannelIds)
              );
            }
          }
        }
      }
    });

    sub.on("subscribed", async () => {
      // Check presence when subscribed
    });

    sub.on("error", (ctx: any) => {
      console.error(`Subscription error: ${ctx.message}`);
    });

    // Connect to Centrifuge and subscribe
    centrifugeClient.connect();
    sub.subscribe();
    setSubscription(sub);

    // Cleanup on component unmount
    return () => {
      sub.unsubscribe();
      centrifugeClient.disconnect();
    };
  }, [connectUrl, dispatch]);

  // centrifugo connection
  // useEffect(() => {
  //   if (notifications) {
  //     if (id2 !== notifications?.channel_id) {
  //       playSound();
  //       toast.info(
  //         <Notify
  //           name={notifications?.full_name}
  //           message={notifications?.message}
  //           image={notifications?.avatar_url}
  //         />,
  //         {
  //           autoClose: 10000,
  //           position: "top-center",
  //           closeOnClick: true,
  //           icon: false,
  //         }
  //       );
  //     }
  //   }
  // }, [notifications, state?.notifications]);

  const playSound = () => {
    audioPlayer?.current?.play();
  };

  //
  return (
    <div>
      <ToastContainer limit={1} />
      <audio controls ref={audioPlayer} style={{ display: "none" }}>
        <source src="/audio/message.mp3" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>
    </div>
  );
};

export default DMNotifications;
