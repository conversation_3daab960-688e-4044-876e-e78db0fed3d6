"use client";
import React from "react";
import SettingsLabel from "../../components/settings-label";
import BillingHeader from "./components/billing-header";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "~/components/ui/tabs";
import BillingPlanCard from "./components/billing-plan-card";
import FeatureList from "./components/billing-feature-list";
import BilllingAICreditBalance from "./components/billing-ai-credit-balance";
import BillingTopUpCredits from "./components/billing-top-up-credit";
import AICreditsUsage from "./components/billing-ai-credit-usage-table";
import { useRouter } from "next/navigation";
import BillingPaymentHistoryTable from "./components/billing-payment-history-table";

const page = () => {
  // eslint-disable-next-line
  const router = useRouter();

  return (
    <div>
      <SettingsLabel />
      <div className="p-4">
        <div className="mb-6">
          <h1 className="text-base font-semibold">
            Your Organisation Billing Information
          </h1>
          <p className="text-sm text-[#344054]">
            Securely manage your organization’s billing details, payment
            history, and subscriptions.
          </p>
        </div>

        <BillingHeader
          title="Telex Free"
          description="You are enjoying the full Telex experience with ability to add as many users to your organisation."
          onComparePlan={() =>
            router.push("/client/settings/organisation/billing/all-plans")
          }
        />

        <Tabs defaultValue="plan-details" className="w-full mt-6">
          <div className="border-b border-gray-200">
            <TabsList className="flex w-fit bg-white rounded-none h-auto p-0 space-x-10">
              <TabsTrigger
                value="plan-details"
                className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors"
              >
                Plan Details
              </TabsTrigger>
              <TabsTrigger
                value="ai-credits"
                className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors"
              >
                AI Credits
              </TabsTrigger>
              <TabsTrigger
                value="payment-history"
                className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors"
              >
                Payment History
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="plan-details" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
              <div className="col-span-2 h-full">
                <BillingPlanCard
                  planName="Telex Free"
                  description="For freelancers, or small early-stage teams who want to explore automation without platform fees."
                  price={0}
                  currency="$"
                  period="per month"
                  usersAdded={1}
                  storageUsed={0.89}
                  totalStorage={10}
                  storageUnit="GB"
                />
              </div>
              <div className="col-span-3 h-full">
                <FeatureList
                  features={[
                    "No user limit",
                    "No monthly platform charge",
                    "Access to all free agents",
                    "Pay as you go fee per paid agent usage",
                    "10GB all-time data storage",
                  ]}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="ai-credits" className="mt-6 ">
            <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
              <div className="col-span-2 h-full">
                <BilllingAICreditBalance
                  totalCredit={4000}
                  creditUsed={1550}
                  creditUnit="Credits"
                />
              </div>
              <div className="col-span-4 h-full">
                <BillingTopUpCredits creditPrice={1} creditsPerDollar={10} />
              </div>
            </div>
            <AICreditsUsage />
          </TabsContent>

          <TabsContent value="payment-history" className="mt-6">
            <BillingPaymentHistoryTable />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default page;
