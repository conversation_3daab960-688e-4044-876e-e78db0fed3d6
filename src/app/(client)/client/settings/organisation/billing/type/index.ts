export type HeaderProps = {
  title: string;
  nextBillingDate?: string;
  description: string;
  onComparePlan?: () => void;
};

export type PlanCardProps = {
  planName: string;
  description: string;
  price: number;
  currency: string;
  period: string;
  usersAdded: number;
  storageUsed: number;
  totalStorage: number;
  storageUnit: string;
};

export type FeatureListProps = {
  features: string[];
};

export type TopUpCreditsProps = {
  creditPrice: number;
  creditsPerDollar: number;
  // eslint-disable-next-line
  onPurchase?: (credits: number) => void;
};

export type AIUsageRecord = {
  id: string;
  agentName: string;
  agentType:
    | "error-handler"
    | "profanity-filter"
    | "content-moderator"
    | "translator"
    | "knowledge-base"
    | "analytics"
    | "scheduler";
  nameOwner: string;
  type: "Channel" | "Private Chat" | "Private Group";
  aiCredits: number;
  dateTime: string;
  status: "online" | "offline";
};

export type PricingTier = {
  name: string;
  description: string;
  price: number;
  period: string;
  features: string[];
  buttonText: string;
  isCurrentPlan?: boolean;
  isHighlighted?: boolean;
};

export type TelexTransaction = {
  id: string;
  description: string;
  extraDescription: string;
  servicePeriod?: string;
  date: string;
  amount: {
    base: number;
    vat: number;
    vatRate: number;
    total: number;
  };
  paymentMethod: {
    type: "visa" | "mastercard" | "amex" | "paypal" | "bank_transfer";
    lastFour: string;
    processor: "stripe" | "paypal" | "direct";
  };
  invoiceNumber?: string;
  status: "paid" | "pending" | "failed" | "refunded";
  currency: string;
};
