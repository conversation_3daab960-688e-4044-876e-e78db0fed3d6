// import Image from "next/image";
// const Features+__ = () => {
//   const features = [
//     {
//       icon: "/images/secure-icon.png",
//       title: "Secure, Instant Access",
//       description:
//         "Log in with your email, jump into channels, and chat in seconds.",
//     },
//     {
//       icon: "/images/bell-icon.png",
//       title: "Never Miss a Message",
//       description:
//         "With notification counts, you’ll always know what’s happening.",
//     },
//     {
//       icon: "/images/loop-icon.png",
//       title: "Stay in the Loop",
//       description:
//         "Catch up on what you missed, no matter how long ago it was.",
//     },
//     {
//       icon: "/images/visibility-icon.png",
//       title: "Full Member Visibility",
//       description: "Known who’s part of the conversation.",
//     },
//     {
//       icon: "/images/switch-icon.png",
//       title: "Switch Workspace Instantly",
//       description:
//         "Hop between organizations without logging out. Perfect for multi-team pros.",
//     },
//     {
//       icon: "/images/ui-icon.png",
//       title: "AI Agents",
//       description: "Everything you need in one space",
//     },
//   ];

//   return (
//     <div className="bg-[#fff] px-2 sm:px-10 lg:px-24" id="choose-us">
//       <div className="flex flex-col justify-between items-center">
//         <div className="flex items-center flex-col space-y-2 text-center w-[95%] md:w-[70%] lg:w-[60%] mx-auto">
//           <h1 className="text-2xl md:text-3xl lg:text-4xl font-semibold">
//             Why Choose Us
//           </h1>
//           <p className="text-[#6D7482] font-semibold w-[70%] text-sm sm:text-lg">
//             These are the reasons you should go with Telex
//           </p>
//         </div>

//         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 place-items-center">
//           {features.map((feature, index) => (
//             <div className="w-full" key={index}>
//               <div className="p-3 bg-[#303073] rounded-br-3xl relative -bottom-1/2 translate-y-7 inline-block ml-4">
//                 <Image alt="icons" width={20} height={20} src={feature.icon} />
//               </div>
//               <div className="w-full min-w-[300px] h-[170px] border border-[#C1C6CE] rounded-br-[40px] flex items-center px-4">
//                 <div className="space-y-3">
//                   <h3 className="font-bold text-lg">{feature.title}</h3>
//                   <p className="text-[#1E1E1E] text-xs">
//                     {feature.description}
//                   </p>
//                 </div>
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>
//     </div>
//   );
// };
// export default Features__;

import Image from "next/image";
const Features__ = () => {
  // const features = [
  //     {
  //         icon: "/images/flash.svg",
  //         title: "Real time notification",
  //         description:
  //             "Get real time alerts for critical activities happening in your server",
  //     },
  //     {
  //         icon: "/images/collaboration.svg",
  //         title: "Team Collaboration",
  //         description:
  //             "Communicate and work with your team in one place",
  //     },
  //     {
  //         icon: "/images/file.svg",
  //         title: "Chat with AI Agents",
  //         description:
  //             "Talk to AI like a teammate in DMs or group channels to monitor activities on your website.",
  //     }
  // ]

  const features = [
    {
      icon: "/images/shield.svg",
      title: "All-in-One App",
      description:
        "Chat, share files, and manage tasks in one place—no more switching between apps.",
    },
    {
      icon: "/images/message.svg",
      title: "Organized Conversations",
      description:
        "Use threads and channels to keep chats clear and easy to follow, so nothing gets lost.",
    },
    {
      icon: "/images/notification.svg",
      title: "Instant Notifications",
      description:
        "Get timely reminders for urgent messages or upcoming deadlines, so your team never misses a beat.",
    },
    {
      icon: "/images/people-share.svg",
      title: "Easy File Sharing",
      description:
        "Upload and find files quickly, so you don’t waste time searching for documents or images.",
    },
    {
      icon: "/images/organization-line.svg",
      title: "AI Helpers",
      description:
        "Hop between organizations without logging out. Perfect for multi-team pros.",
    },
    {
      icon: "/images/sparkle.svg",
      title: "Affordable for Small Businesses",
      description: "Everything you need, redesigned for speed and simplicity.",
    },
  ];

  return (
    <div className="px-2 sm:px-10 lg:px-24" id="why-telex">
      <div className="flex flex-col justify-between items-center gap-8 container">
        <div className="flex items-center flex-col space-y-2 text-center w-[95%] md:w-[70%] lg:w-[60%] mx-auto">
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-semibold">
            Why Choose Telex
          </h1>
          <p className="text-[#6D7482] font-semibold w-[95%] sm:w-[70%] text-sm sm:text-lg">
            Telex is the Tool for Team collaboration and Workplace
            Communication.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-12 gap-y-6 place-items-center">
          {features.map((feature, index) => (
            // <div className="w-full" key={index}>
            //   <div className="p-3 bg-[#303073] rounded-br-3xl relative -bottom-1/2 translate-y-7 inline-block ml-4">
            //     <Image alt="icons" width={20} height={20} src={feature.icon} />
            //   </div>
            //   <div className="w-full min-w-[300px] h-[170px] border border-[#C1C6CE] rounded-br-[40px] flex items-center px-4">
            //     <div className="space-y-3">
            //       <h3 className="font-bold text-lg">{feature.title}</h3>
            //       <p className="text-[#1E1E1E] text-xs">
            //         {feature.description}
            //       </p>
            //     </div>
            //   </div>
            // </div>

            <div className="w-full space-y-2" key={index}>
              <div className="flex justify-center">
                <Image
                  src={feature.icon}
                  alt="icons"
                  className="max-sm:w-9"
                  width={64}
                  height={64}
                />
              </div>
              <div className="flex flex-col items-center justify-center text-center text-[#1E1E1E] p-4 space-y-2">
                <h3 className="font-bold text-lg">{feature.title}</h3>
                <p className="text-[#615F5F] text-sm">{feature.description}</p>
              </div>
            </div>

            // <div className="border border-[#C1C6CE] rounded-lg p-3 flex flex-col items-center justify-center gap-6 text-center h-[300px] md:h-[400px] hover:border-[#2A2B67]" key={index}>
            //     <div className="flex justify-center">
            //         <Image src={feature.icon} alt="icons" className="max-sm:w-9" width={60} height={60} />
            //     </div>
            //     <div className="flex flex-col items-center justify-center text-center text-[#1E1E1E] p-4 space-y-4">
            //         <h1 className="font-semibold text-lg md:text-2xl lg:text-4xl">{feature.title}</h1>
            //         <p className="text-[#615F5F] text-sm md:text-lg">{feature.description}</p>
            //     </div>
            // </div>
          ))}
        </div>
      </div>
    </div>
  );
};
export default Features__;
